#!/usr/bin/env python3
"""
Simple analysis of signal oscillation without external dependencies.
"""

import json
from datetime import datetime
from pathlib import Path

def analyze_threshold_file(threshold_file):
    """Analyze threshold breach patterns."""
    
    if not Path(threshold_file).exists():
        print(f"❌ Threshold file not found: {threshold_file}")
        return
    
    print("🔍 ANALYZING SIGNAL OSCILLATION")
    print("=" * 50)
    
    # Read and parse file
    breaches = []
    with open(threshold_file, 'r') as f:
        lines = f.readlines()
    
    for line in lines:
        if '|' in line and ('TRIGGERED' in line or 'NEAR_MISS' in line):
            parts = line.strip().split('|')
            if len(parts) >= 6:
                try:
                    timestamp_str = parts[0].strip()
                    signal_value = float(parts[1].strip())
                    threshold = float(parts[3].strip())
                    signal_type = parts[4].strip()
                    result = parts[5].strip()
                    
                    breaches.append({
                        'timestamp': timestamp_str,
                        'signal_value': signal_value,
                        'threshold': threshold,
                        'signal_type': signal_type,
                        'result': result
                    })
                except:
                    continue
    
    if not breaches:
        print("❌ No breach data found")
        return
    
    print(f"📊 Total breach records: {len(breaches)}")
    
    # Basic statistics
    signals = [b['signal_value'] for b in breaches]
    signal_min = min(signals)
    signal_max = max(signals)
    signal_mean = sum(signals) / len(signals)
    
    print(f"\n🎯 SIGNAL STATISTICS:")
    print(f"   Range: [{signal_min:.4f}, {signal_max:.4f}]")
    print(f"   Mean: {signal_mean:.4f}")
    
    # Count extreme signals
    extreme_positive = sum(1 for s in signals if s > 0.9)
    extreme_negative = sum(1 for s in signals if s < -0.9)
    extreme_total = extreme_positive + extreme_negative
    
    print(f"\n🔥 EXTREME SIGNALS (>0.9 or <-0.9):")
    print(f"   Positive extreme: {extreme_positive}")
    print(f"   Negative extreme: {extreme_negative}")
    print(f"   Total extreme: {extreme_total}")
    print(f"   Percentage: {extreme_total/len(signals)*100:.1f}%")
    
    # Analyze oscillation patterns
    sign_changes = 0
    rapid_oscillations = 0
    
    for i in range(1, len(breaches)):
        prev_signal = breaches[i-1]['signal_value']
        curr_signal = breaches[i]['signal_value']
        
        # Check for sign change
        if (prev_signal > 0) != (curr_signal > 0):
            sign_changes += 1
            
            # Check if both are extreme (potential rapid oscillation)
            if abs(prev_signal) > 0.8 and abs(curr_signal) > 0.8:
                rapid_oscillations += 1
    
    print(f"\n⚡ OSCILLATION ANALYSIS:")
    print(f"   Sign changes: {sign_changes}")
    print(f"   Rapid oscillations (extreme→extreme): {rapid_oscillations}")
    print(f"   Oscillation rate: {sign_changes/len(signals)*100:.1f}%")
    
    # Show examples of rapid oscillations
    print(f"\n🔄 RAPID OSCILLATION EXAMPLES:")
    examples_shown = 0
    for i in range(1, min(len(breaches), 100)):  # Check first 100 for examples
        prev_signal = breaches[i-1]['signal_value']
        curr_signal = breaches[i]['signal_value']
        
        if (abs(prev_signal) > 0.8 and abs(curr_signal) > 0.8 and 
            (prev_signal > 0) != (curr_signal > 0)):
            print(f"   {breaches[i-1]['timestamp']}: {prev_signal:.3f}")
            print(f"   {breaches[i]['timestamp']}: {curr_signal:.3f}")
            print(f"   ---")
            examples_shown += 1
            if examples_shown >= 3:
                break
    
    # Diagnosis and recommendations
    print(f"\n🔬 DIAGNOSIS:")
    
    if extreme_total / len(signals) > 0.7:
        print("   ⚠️  TOO MANY EXTREME SIGNALS (>70%)")
        print("      → Agent generates mostly extreme values")
        print("      → Possible VecNormalize or feature normalization issue")
    
    if sign_changes / len(signals) > 0.3:
        print("   ⚠️  HIGH OSCILLATION RATE (>30%)")
        print("      → Signals change direction too frequently")
        print("      → Need signal smoothing")
    
    if rapid_oscillations > 10:
        print("   ⚠️  RAPID EXTREME OSCILLATIONS")
        print("      → Agent switches between extreme confidence rapidly")
        print("      → Unstable features or model behavior")
    
    print(f"\n✅ SOLUTIONS IMPLEMENTED:")
    print("   1. Signal EMA smoothing added to simulate_trading_new.py")
    print("   2. Threshold adjusted from 0.996 to 0.6")
    print("   3. Orderbook volume normalization available")
    
    print(f"\n🎯 NEXT STEPS:")
    print("   1. Test with signal smoothing:")
    print("      python3 simulate_trading_new.py --config strategyConfig_scalp_1s.json")
    print("   2. Compare new threshold breach patterns")
    print("   3. If still oscillating, increase EMA alpha to 0.5")

def create_stable_config():
    """Create a configuration optimized for signal stability."""
    
    base_config = "strategyConfig_scalp_1s.json"
    output_config = "strategyConfig_scalp_1s_stable.json"
    
    try:
        with open(base_config, 'r') as f:
            config = json.load(f)
        
        # Optimize for stability
        config['tradeParams']['entryActionThreshold'] = 0.5
        config['tradeParams']['longEntryThreshold'] = 0.5
        config['tradeParams']['shortEntryThreshold'] = 0.5
        config['tradeParams']['exitActionThreshold'] = 0.4
        
        # Add stability settings
        config['signalStability'] = {
            "emaAlpha": 0.3,
            "stabilityWindow": 3,
            "decisionInterval": 5,
            "description": "Signal smoothing parameters"
        }
        
        with open(output_config, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"✅ Created stable config: {output_config}")
        
    except Exception as e:
        print(f"❌ Error creating stable config: {e}")

def main():
    threshold_file = "threshold_breaches_20250712_093253.txt"
    
    # Analyze current patterns
    analyze_threshold_file(threshold_file)
    
    # Create stable configuration
    create_stable_config()
    
    print(f"\n" + "="*60)
    print("🚀 SUMMARY")
    print("="*60)
    print("Problem: Agent generates rapidly oscillating extreme signals")
    print("Cause: Unstable orderbook features + no signal smoothing")
    print("Solution: EMA smoothing + moderate thresholds + feature normalization")
    print("Status: Signal smoothing IMPLEMENTED in simulate_trading_new.py")
    print("Next: Test and compare results")

if __name__ == "__main__":
    main()
