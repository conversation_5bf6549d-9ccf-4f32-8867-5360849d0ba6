#!/usr/bin/env python3
"""
Analyze and fix signal oscillation issues in trading agent.
This script identifies causes of rapid signal changes and implements solutions.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import logging
import json
from datetime import datetime, timedelta
import argparse

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)

def analyze_threshold_breaches(threshold_file):
    """Analyze threshold breach patterns to identify oscillation."""
    
    if not Path(threshold_file).exists():
        log.error(f"Threshold file not found: {threshold_file}")
        return None
    
    # Read threshold breach data
    breaches = []
    with open(threshold_file, 'r') as f:
        lines = f.readlines()
    
    # Parse breach data
    for line in lines:
        if '|' in line and ('TRIGGERED' in line or 'NEAR_MISS' in line):
            parts = line.strip().split('|')
            if len(parts) >= 6:
                try:
                    timestamp_str = parts[0].strip()
                    signal_value = float(parts[1].strip())
                    threshold = float(parts[3].strip())
                    signal_type = parts[4].strip()
                    result = parts[5].strip()
                    
                    # Parse timestamp
                    timestamp = pd.to_datetime(timestamp_str)
                    
                    breaches.append({
                        'timestamp': timestamp,
                        'signal_value': signal_value,
                        'threshold': threshold,
                        'signal_type': signal_type,
                        'result': result,
                        'abs_signal': abs(signal_value)
                    })
                except:
                    continue
    
    if not breaches:
        log.warning("No breach data found")
        return None
    
    df = pd.DataFrame(breaches)
    
    # Analysis
    log.info(f"📊 SIGNAL OSCILLATION ANALYSIS")
    log.info(f"Total breach records: {len(df)}")
    log.info(f"Time range: {df['timestamp'].min()} to {df['timestamp'].max()}")
    
    # 1. Signal distribution
    log.info(f"\n🎯 SIGNAL DISTRIBUTION:")
    log.info(f"Mean signal: {df['signal_value'].mean():.4f}")
    log.info(f"Std signal: {df['signal_value'].std():.4f}")
    log.info(f"Signal range: [{df['signal_value'].min():.4f}, {df['signal_value'].max():.4f}]")
    
    # 2. Oscillation detection
    df = df.sort_values('timestamp')
    df['signal_change'] = df['signal_value'].diff()
    df['sign_change'] = ((df['signal_value'] > 0) != (df['signal_value'].shift(1) > 0))
    
    rapid_oscillations = df[df['sign_change'] == True]
    time_between_oscillations = rapid_oscillations['timestamp'].diff()
    
    log.info(f"\n⚡ OSCILLATION PATTERNS:")
    log.info(f"Sign changes: {rapid_oscillations.shape[0]}")
    log.info(f"Avg time between sign changes: {time_between_oscillations.mean()}")
    
    # Find rapid oscillations (sign changes within 10 seconds)
    rapid_mask = time_between_oscillations < timedelta(seconds=10)
    rapid_count = rapid_mask.sum()
    
    log.info(f"Rapid oscillations (<10s): {rapid_count}")
    log.info(f"Oscillation rate: {rapid_count/len(df)*100:.1f}%")
    
    # 3. Extreme signal analysis
    extreme_signals = df[df['abs_signal'] > 0.9]
    log.info(f"\n🔥 EXTREME SIGNALS (>0.9):")
    log.info(f"Count: {len(extreme_signals)}")
    log.info(f"Percentage: {len(extreme_signals)/len(df)*100:.1f}%")
    
    # 4. Consecutive opposite signals
    df['prev_signal'] = df['signal_value'].shift(1)
    opposite_signals = df[
        ((df['signal_value'] > 0.8) & (df['prev_signal'] < -0.8)) |
        ((df['signal_value'] < -0.8) & (df['prev_signal'] > 0.8))
    ]
    
    log.info(f"\n🔄 CONSECUTIVE OPPOSITE EXTREME SIGNALS:")
    log.info(f"Count: {len(opposite_signals)}")
    log.info(f"Examples:")
    for _, row in opposite_signals.head(5).iterrows():
        log.info(f"  {row['timestamp']}: {row['prev_signal']:.3f} → {row['signal_value']:.3f}")
    
    return df

def generate_signal_stability_config(base_config_path, output_path):
    """Generate configuration with signal stability improvements."""
    
    with open(base_config_path, 'r') as f:
        config = json.load(f)
    
    # Add signal stability parameters
    config['signalStability'] = {
        "enabled": True,
        "emaSmoothing": {
            "enabled": True,
            "alpha": 0.3,
            "description": "EMA smoothing factor for entry signals"
        },
        "stabilityWindow": {
            "enabled": True,
            "windowSize": 3,
            "threshold": 0.5,
            "description": "Require consistent signals over N steps"
        },
        "decisionThrottling": {
            "enabled": True,
            "intervalSeconds": 5,
            "description": "Minimum seconds between entry decisions"
        },
        "oscillationPrevention": {
            "enabled": True,
            "maxSignalChange": 0.8,
            "timeWindow": 10,
            "description": "Prevent rapid signal oscillations"
        }
    }
    
    # Adjust thresholds for stability
    original_threshold = config['tradeParams']['entryActionThreshold']
    config['tradeParams']['entryActionThreshold'] = min(0.7, original_threshold + 0.1)
    config['tradeParams']['longEntryThreshold'] = min(0.7, original_threshold + 0.1)
    config['tradeParams']['shortEntryThreshold'] = min(0.7, original_threshold + 0.1)
    
    # Add feature smoothing
    config['featureStability'] = {
        "orderbookSmoothing": {
            "enabled": True,
            "alpha": 0.2,
            "description": "Smooth orderbook volume features"
        },
        "indicatorSmoothing": {
            "enabled": True,
            "alpha": 0.1,
            "description": "Smooth technical indicators"
        }
    }
    
    # Save enhanced config
    with open(output_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    log.info(f"✅ Generated stability-enhanced config: {output_path}")
    return output_path

def create_signal_smoothing_wrapper():
    """Create a wrapper class for signal smoothing."""
    
    wrapper_code = '''
class SignalSmoothingWrapper:
    """Wrapper to smooth agent signals and prevent oscillation."""
    
    def __init__(self, agent, ema_alpha=0.3, stability_window=3, stability_threshold=0.5):
        self.agent = agent
        self.ema_alpha = ema_alpha
        self.stability_window = stability_window
        self.stability_threshold = stability_threshold
        
        # State tracking
        self.action_ema = None
        self.recent_actions = []
        self.last_decision_time = 0
        self.decision_interval = 5  # seconds
        
    def predict(self, observation, deterministic=True):
        """Predict with signal smoothing."""
        import time
        import numpy as np
        
        # Get raw prediction from agent
        raw_action, state = self.agent.predict(observation, deterministic=deterministic)
        
        if not isinstance(raw_action, np.ndarray) or raw_action.size < 4:
            return raw_action, state
        
        raw_action = np.clip(raw_action.flatten(), -1.0, 1.0)
        raw_entry_sig = raw_action[0]
        
        # Apply EMA smoothing
        if self.action_ema is None:
            self.action_ema = raw_entry_sig
        else:
            self.action_ema = self.ema_alpha * raw_entry_sig + (1 - self.ema_alpha) * self.action_ema
        
        # Create smoothed action
        smoothed_action = raw_action.copy()
        smoothed_action[0] = self.action_ema
        
        # Track recent actions for stability
        self.recent_actions.append(self.action_ema)
        if len(self.recent_actions) > self.stability_window:
            self.recent_actions.pop(0)
        
        # Check signal stability
        if len(self.recent_actions) >= self.stability_window:
            signal_std = np.std(self.recent_actions)
            if signal_std > self.stability_threshold:
                # Reduce signal strength if unstable
                smoothed_action[0] *= 0.5
        
        # Decision throttling
        current_time = time.time()
        if current_time - self.last_decision_time < self.decision_interval:
            if abs(smoothed_action[0]) < 0.8:  # Only throttle weak signals
                smoothed_action[0] *= 0.3
        else:
            self.last_decision_time = current_time
        
        return smoothed_action, state
'''
    
    with open('signal_smoothing_wrapper.py', 'w') as f:
        f.write(wrapper_code)
    
    log.info("✅ Created signal smoothing wrapper: signal_smoothing_wrapper.py")

def main():
    parser = argparse.ArgumentParser(description='Analyze and fix signal oscillation')
    parser.add_argument('--threshold-file', default='threshold_breaches_20250712_093253.txt',
                       help='Threshold breach log file')
    parser.add_argument('--config', default='strategyConfig_scalp_1s.json',
                       help='Base configuration file')
    parser.add_argument('--output-config', default='strategyConfig_scalp_1s_stable.json',
                       help='Output configuration with stability improvements')
    
    args = parser.parse_args()
    
    log.info("🔍 SIGNAL OSCILLATION ANALYSIS AND FIX")
    log.info("=" * 50)
    
    # 1. Analyze current oscillation patterns
    log.info("1. Analyzing threshold breach patterns...")
    df_analysis = analyze_threshold_breaches(args.threshold_file)
    
    if df_analysis is not None:
        # 2. Generate stability-enhanced configuration
        log.info("\n2. Generating stability-enhanced configuration...")
        generate_signal_stability_config(args.config, args.output_config)
        
        # 3. Create signal smoothing wrapper
        log.info("\n3. Creating signal smoothing wrapper...")
        create_signal_smoothing_wrapper()
        
        # 4. Recommendations
        log.info("\n🎯 RECOMMENDATIONS:")
        log.info("=" * 30)
        
        signal_std = df_analysis['signal_value'].std()
        oscillation_rate = (df_analysis['sign_change'].sum() / len(df_analysis)) * 100
        
        if signal_std > 0.8:
            log.info("⚠️  HIGH SIGNAL VOLATILITY detected")
            log.info("   → Use stronger EMA smoothing (alpha=0.2)")
            log.info("   → Increase stability window to 5")
        
        if oscillation_rate > 30:
            log.info("⚠️  HIGH OSCILLATION RATE detected")
            log.info("   → Enable decision throttling (5s intervals)")
            log.info("   → Increase entry thresholds by 0.1")
        
        extreme_pct = (df_analysis['abs_signal'] > 0.9).sum() / len(df_analysis) * 100
        if extreme_pct > 50:
            log.info("⚠️  TOO MANY EXTREME SIGNALS detected")
            log.info("   → Check VecNormalize loading")
            log.info("   → Apply orderbook volume normalization")
        
        log.info("\n✅ NEXT STEPS:")
        log.info("1. Test with new config:")
        log.info(f"   python simulate_trading_new.py --config {args.output_config}")
        log.info("2. Compare oscillation patterns")
        log.info("3. Adjust smoothing parameters if needed")
        
    else:
        log.error("❌ Could not analyze threshold data")

if __name__ == "__main__":
    main()
