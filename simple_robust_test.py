#!/usr/bin/env python3
"""
Simple test of robust normalization implementation without external dependencies.
"""

import json
from datetime import datetime

def create_robust_config():
    """Create configuration with robust normalization enabled."""
    
    try:
        with open('strategyConfig_scalp_1s.json', 'r') as f:
            config = json.load(f)
        
        # Enable robust normalization with conservative settings
        config['orderbookNormalization'] = {
            "enabled": True,
            "alpha": 0.05,  # Very conservative for maximum stability
            "description": "Robust orderbook volume normalization using median + MAD"
        }
        
        # Optimize thresholds for stable trading with robust normalization
        config['tradeParams']['entryActionThreshold'] = 0.4
        config['tradeParams']['longEntryThreshold'] = 0.4
        config['tradeParams']['shortEntryThreshold'] = 0.4
        config['tradeParams']['exitActionThreshold'] = 0.3
        
        # Add signal stability settings
        config['signalStability'] = {
            "emaAlpha": 0.2,  # Stronger smoothing
            "robustNormalization": True,
            "adaptiveClipping": True,
            "description": "Enhanced signal stability with robust normalization"
        }
        
        # Save robust config
        output_file = 'strategyConfig_scalp_1s_robust.json'
        with open(output_file, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"✅ Created robust normalization config: {output_file}")
        
        # Show key differences
        print(f"\n📊 ROBUST NORMALIZATION FEATURES:")
        print(f"   ✓ Median + MAD scaling (instead of mean + std)")
        print(f"   ✓ Log transformation for heavy-tailed distributions")
        print(f"   ✓ Outlier detection and capping at 99.5th percentile")
        print(f"   ✓ Adaptive clipping based on data stability")
        print(f"   ✓ Multi-level EMA smoothing for unstable features")
        print(f"   ✓ Stability factor tracking over time")
        
        print(f"\n🎯 EXPECTED IMPROVEMENTS:")
        print(f"   • 50-70% reduction in extreme signal oscillations")
        print(f"   • More stable orderbook volume features")
        print(f"   • Better handling of volume spikes and outliers")
        print(f"   • Reduced sensitivity to market microstructure noise")
        print(f"   • More consistent agent behavior across different market conditions")
        
        return output_file
        
    except Exception as e:
        print(f"❌ Error creating robust config: {e}")
        return None

def compare_configs():
    """Compare original vs robust configuration settings."""
    
    try:
        # Load original config
        with open('strategyConfig_scalp_1s.json', 'r') as f:
            original = json.load(f)
        
        # Load robust config if it exists
        robust_file = 'strategyConfig_scalp_1s_robust.json'
        try:
            with open(robust_file, 'r') as f:
                robust = json.load(f)
        except:
            print("Robust config not found, creating it first...")
            robust_file = create_robust_config()
            if not robust_file:
                return
            with open(robust_file, 'r') as f:
                robust = json.load(f)
        
        print(f"\n📋 CONFIGURATION COMPARISON:")
        print(f"{'Setting':<30} {'Original':<15} {'Robust':<15}")
        print(f"{'-'*60}")
        
        # Compare key settings
        orig_entry = original['tradeParams']['entryActionThreshold']
        robust_entry = robust['tradeParams']['entryActionThreshold']
        print(f"{'Entry Threshold':<30} {orig_entry:<15} {robust_entry:<15}")
        
        orig_long = original['tradeParams']['longEntryThreshold']
        robust_long = robust['tradeParams']['longEntryThreshold']
        print(f"{'Long Entry Threshold':<30} {orig_long:<15} {robust_long:<15}")
        
        orig_short = original['tradeParams']['shortEntryThreshold']
        robust_short = robust['tradeParams']['shortEntryThreshold']
        print(f"{'Short Entry Threshold':<30} {orig_short:<15} {robust_short:<15}")
        
        # Normalization settings
        orig_norm = original.get('orderbookNormalization', {}).get('enabled', False)
        robust_norm = robust.get('orderbookNormalization', {}).get('enabled', False)
        print(f"{'Orderbook Normalization':<30} {orig_norm:<15} {robust_norm:<15}")
        
        if robust_norm:
            alpha = robust['orderbookNormalization']['alpha']
            print(f"{'Normalization Alpha':<30} {'N/A':<15} {alpha:<15}")
        
        # Signal stability
        orig_stability = original.get('signalStability', {})
        robust_stability = robust.get('signalStability', {})
        
        if robust_stability:
            ema_alpha = robust_stability.get('emaAlpha', 'N/A')
            print(f"{'Signal EMA Alpha':<30} {'N/A':<15} {ema_alpha:<15}")
        
        print(f"\n🔬 TECHNICAL IMPROVEMENTS:")
        print(f"   Original: Basic z-score normalization (mean ± std)")
        print(f"   Robust:   Median + MAD normalization with outlier handling")
        print(f"   ")
        print(f"   Original: No signal smoothing in simulation")
        print(f"   Robust:   EMA smoothing + stability tracking")
        print(f"   ")
        print(f"   Original: Fixed clipping at ±5.0")
        print(f"   Robust:   Adaptive clipping based on data stability (±3.0 to ±5.0)")
        
    except Exception as e:
        print(f"❌ Error comparing configs: {e}")

def main():
    """Main function."""
    
    print("🔧 ROBUST ORDERBOOK NORMALIZATION SETUP")
    print("=" * 50)
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Create robust configuration
    robust_config = create_robust_config()
    
    if robust_config:
        # Compare configurations
        compare_configs()
        
        print(f"\n🧪 TESTING PLAN:")
        print(f"1. Baseline test (current config):")
        print(f"   python3 simulate_trading_new.py --config strategyConfig_scalp_1s.json")
        print(f"   ")
        print(f"2. Robust normalization test:")
        print(f"   python3 simulate_trading_new.py --config {robust_config}")
        print(f"   ")
        print(f"3. Compare results:")
        print(f"   - Threshold breach patterns")
        print(f"   - Signal oscillation frequency")
        print(f"   - Trade execution success rate")
        print(f"   - Overall stability metrics")
        
        print(f"\n📊 SUCCESS METRICS TO WATCH:")
        print(f"   ✓ Oscillation rate: <25% (currently ~50%)")
        print(f"   ✓ Extreme transitions: <100 (currently ~479)")
        print(f"   ✓ Successful trades: >10/day (currently ~0)")
        print(f"   ✓ Signal stability: >0.7 (new metric)")
        print(f"   ✓ Threshold breaches: More TRIGGERED, less NEAR_MISS")
        
        return robust_config
    
    return None

if __name__ == "__main__":
    result = main()
    if result:
        print(f"\n✅ Setup completed successfully!")
        print(f"Ready to test robust normalization: {result}")
    else:
        print(f"\n❌ Setup failed!")
