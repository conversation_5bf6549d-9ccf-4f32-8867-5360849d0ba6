#!/usr/bin/env python3
"""
Test script to verify the NameError fix without running full simulation.
"""

import re
from datetime import datetime

def check_variable_usage():
    """Check for undefined variable usage in simulate_trading_new.py."""
    
    print("🔍 VERIFYING NAMEERROR FIX")
    print("=" * 40)
    
    try:
        with open('simulate_trading_new.py', 'r') as f:
            content = f.read()
        
        # Check for problematic patterns
        issues = []
        
        # 1. Check for 'row' usage (should be 'current_row' or similar)
        row_matches = re.findall(r'\brow\[', content)
        if row_matches:
            issues.append(f"Found {len(row_matches)} instances of 'row[' usage")
        
        # 2. Check for proper variable definitions in the signal aggregation section
        lines = content.split('\n')
        in_signal_section = False
        
        for i, line in enumerate(lines, 1):
            # Look for signal aggregation section
            if 'Time-based filtering' in line:
                in_signal_section = True
                
            if in_signal_section and i < len(lines) - 10:  # Check next 10 lines
                # Check for undefined variables
                if 'row[' in line and 'current_row' not in lines[max(0, i-10):i]:
                    issues.append(f"Line {i}: Potential undefined 'row' variable")
                
                # Check if we're past the problematic section
                if 'Debug signal processing' in line:
                    in_signal_section = False
        
        # 3. Check for proper timestamp handling
        timestamp_patterns = [
            r'pd\.Timestamp\(row\[',  # Should be current_row or current_time
            r'current_time = pd\.Timestamp\(row',  # Should use current_row
        ]
        
        for pattern in timestamp_patterns:
            matches = re.findall(pattern, content)
            if matches:
                issues.append(f"Found problematic timestamp pattern: {pattern}")
        
        # 4. Check for proper fixes
        fixes_found = []
        
        if 'current_time_ts = current_time.timestamp()' in content:
            fixes_found.append("✅ Fixed timestamp conversion")
        
        if 'last_trade_time = current_time.timestamp()' in content:
            fixes_found.append("✅ Fixed last_trade_time assignment")
        
        # Report results
        print("🔧 ISSUES FOUND:")
        if issues:
            for issue in issues:
                print(f"  ❌ {issue}")
        else:
            print("  ✅ No variable definition issues found")
        
        print(f"\n✅ FIXES APPLIED:")
        for fix in fixes_found:
            print(f"  {fix}")
        
        if not fixes_found:
            print("  ⚠️  No fixes detected - may need manual verification")
        
        # Check specific lines around the error
        print(f"\n📍 CHECKING SPECIFIC ERROR LOCATION:")
        error_line_found = False
        for i, line in enumerate(lines, 1):
            if 'current_time = pd.Timestamp(row[' in line:
                print(f"  ❌ Line {i}: Still contains problematic code: {line.strip()}")
                error_line_found = True
            elif 'current_time_ts = current_time.timestamp()' in line:
                print(f"  ✅ Line {i}: Fixed code found: {line.strip()}")
                error_line_found = True
        
        if not error_line_found:
            print("  ✅ No problematic timestamp code found")
        
        return len(issues) == 0
        
    except Exception as e:
        print(f"❌ Error checking file: {e}")
        return False

def verify_signal_aggregation_code():
    """Verify that signal aggregation code is properly implemented."""
    
    print(f"\n🔧 VERIFYING SIGNAL AGGREGATION IMPLEMENTATION")
    print("=" * 50)
    
    try:
        with open('simulate_trading_new.py', 'r') as f:
            content = f.read()
        
        # Check for key signal aggregation components
        components = [
            ('signal_history = []', 'Signal history initialization'),
            ('signal_aggregation_window = 5', 'Aggregation window setting'),
            ('confidence_threshold = 0.65', 'Confidence threshold setting'),
            ('last_trade_time = 0', 'Last trade time initialization'),
            ('min_trade_interval = 30', 'Minimum trade interval setting'),
            ('signal_history.append', 'Signal history tracking'),
            ('weights = [0.5, 0.3, 0.2]', 'Weighted aggregation'),
            ('aggregated_signal = sum', 'Signal aggregation calculation'),
            ('abs(aggregated_signal) > confidence_threshold', 'Confidence filtering'),
            ('current_time_ts - last_trade_time < min_trade_interval', 'Time-based filtering'),
        ]
        
        print("📊 SIGNAL AGGREGATION COMPONENTS:")
        all_found = True
        for component, description in components:
            if component in content:
                print(f"  ✅ {description}")
            else:
                print(f"  ❌ {description} - NOT FOUND")
                all_found = False
        
        # Check for proper variable scope
        print(f"\n🔍 VARIABLE SCOPE CHECK:")
        
        # Variables that should be defined before use
        required_vars = [
            'signal_history',
            'confidence_threshold', 
            'last_trade_time',
            'min_trade_interval',
            'current_time_ts'
        ]
        
        lines = content.split('\n')
        var_definitions = {}
        var_usage = {}
        
        for i, line in enumerate(lines, 1):
            for var in required_vars:
                if f'{var} =' in line and 'if' not in line:
                    var_definitions[var] = i
                elif var in line and f'{var} =' not in line:
                    if var not in var_usage:
                        var_usage[var] = []
                    var_usage[var].append(i)
        
        scope_issues = []
        for var in required_vars:
            if var in var_usage and var in var_definitions:
                first_use = min(var_usage[var])
                definition = var_definitions[var]
                if first_use < definition:
                    scope_issues.append(f"{var}: used at line {first_use}, defined at line {definition}")
        
        if scope_issues:
            print("  ❌ Variable scope issues:")
            for issue in scope_issues:
                print(f"    {issue}")
        else:
            print("  ✅ No variable scope issues found")
        
        return all_found and len(scope_issues) == 0
        
    except Exception as e:
        print(f"❌ Error verifying signal aggregation: {e}")
        return False

def create_test_summary():
    """Create a summary of the fix and next steps."""
    
    print(f"\n📋 FIX SUMMARY")
    print("=" * 30)
    
    print("🔧 APPLIED FIXES:")
    fixes = [
        "Changed 'pd.Timestamp(row['timestamp']).timestamp()' to 'current_time.timestamp()'",
        "Used 'current_time_ts' variable for timestamp operations",
        "Maintained proper variable scope in signal aggregation",
        "Added signal history tracking and aggregation logic",
        "Implemented confidence filtering and time-based filtering"
    ]
    
    for fix in fixes:
        print(f"  ✅ {fix}")
    
    print(f"\n🎯 EXPECTED BEHAVIOR:")
    behaviors = [
        "No more NameError: name 'row' is not defined",
        "Signal aggregation working properly",
        "Time-based filtering preventing rapid trades",
        "Confidence filtering reducing noise",
        "Proper timestamp handling throughout simulation"
    ]
    
    for behavior in behaviors:
        print(f"  • {behavior}")
    
    print(f"\n🚀 NEXT STEPS:")
    steps = [
        "Test with proper Python environment that has pandas",
        "Run simulation with fixed code",
        "Monitor threshold breach patterns for improvements",
        "Verify signal aggregation is reducing oscillations",
        "Compare results with previous runs"
    ]
    
    for i, step in enumerate(steps, 1):
        print(f"  {i}. {step}")

def main():
    """Main verification function."""
    
    print("🔍 NAMEERROR FIX VERIFICATION")
    print("=" * 50)
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Check for variable usage issues
    variables_ok = check_variable_usage()
    
    # Verify signal aggregation implementation
    aggregation_ok = verify_signal_aggregation_code()
    
    # Create summary
    create_test_summary()
    
    # Final verdict
    print(f"\n🎯 VERIFICATION RESULT:")
    if variables_ok and aggregation_ok:
        print("✅ ALL CHECKS PASSED - Fix appears to be successful")
        print("Ready for testing with proper Python environment")
    else:
        print("❌ SOME ISSUES FOUND - May need additional fixes")
        if not variables_ok:
            print("  • Variable definition issues detected")
        if not aggregation_ok:
            print("  • Signal aggregation implementation issues detected")
    
    return variables_ok and aggregation_ok

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
