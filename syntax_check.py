#!/usr/bin/env python3
"""
Simple syntax check for simulate_trading_new.py without importing pandas.
"""

import ast
import sys
from datetime import datetime

def check_syntax():
    """Check Python syntax of simulate_trading_new.py."""
    
    print("🔍 SYNTAX CHECK FOR simulate_trading_new.py")
    print("=" * 50)
    
    try:
        with open('simulate_trading_new.py', 'r') as f:
            content = f.read()
        
        # Try to parse the AST
        try:
            ast.parse(content)
            print("✅ SYNTAX CHECK PASSED")
            print("   No Python syntax errors found")
            return True
        except SyntaxError as e:
            print(f"❌ SYNTAX ERROR FOUND:")
            print(f"   Line {e.lineno}: {e.text}")
            print(f"   Error: {e.msg}")
            return False
        
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return False

def check_variable_definitions():
    """Check for basic variable definition issues."""
    
    print(f"\n🔍 VARIABLE DEFINITION CHECK")
    print("=" * 40)
    
    try:
        with open('simulate_trading_new.py', 'r') as f:
            lines = f.readlines()
        
        # Check for key variables
        key_vars = {
            'last_trade_time': False,
            'signal_history': False,
            'confidence_threshold': False,
            'min_trade_interval': False,
            'current_time_ts': False
        }
        
        for i, line in enumerate(lines, 1):
            for var in key_vars:
                if f'{var} =' in line and not key_vars[var]:
                    key_vars[var] = i
                    print(f"✅ {var} defined at line {i}")
        
        # Check for undefined variables
        missing_vars = [var for var, line_num in key_vars.items() if not line_num]
        if missing_vars:
            print(f"❌ Missing variable definitions: {missing_vars}")
            return False
        else:
            print("✅ All key variables are defined")
            return True
            
    except Exception as e:
        print(f"❌ Error checking variables: {e}")
        return False

def check_specific_fixes():
    """Check for specific fixes that were applied."""
    
    print(f"\n🔧 SPECIFIC FIXES CHECK")
    print("=" * 30)
    
    try:
        with open('simulate_trading_new.py', 'r') as f:
            content = f.read()
        
        fixes = [
            ('current_time_ts = current_time.timestamp()', 'Fixed timestamp conversion'),
            ('last_trade_time = current_time.timestamp()', 'Fixed last_trade_time assignment'),
            ('signal_history.append(action_ema)', 'Signal history tracking'),
            ('aggregated_signal = sum(s * w for s, w', 'Signal aggregation'),
            ('abs(aggregated_signal) > confidence_threshold', 'Confidence filtering'),
            ('current_time_ts - last_trade_time < min_trade_interval', 'Time filtering')
        ]
        
        all_fixes_found = True
        for fix_code, description in fixes:
            if fix_code in content:
                print(f"✅ {description}")
            else:
                print(f"❌ {description} - NOT FOUND")
                all_fixes_found = False
        
        return all_fixes_found
        
    except Exception as e:
        print(f"❌ Error checking fixes: {e}")
        return False

def main():
    """Main check function."""
    
    print("🔧 SIMULATE_TRADING_NEW.PY - COMPREHENSIVE CHECK")
    print("=" * 60)
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Check syntax
    syntax_ok = check_syntax()
    
    # Check variable definitions
    variables_ok = check_variable_definitions()
    
    # Check specific fixes
    fixes_ok = check_specific_fixes()
    
    # Final result
    print(f"\n🎯 FINAL RESULT:")
    print("=" * 20)
    
    if syntax_ok and variables_ok and fixes_ok:
        print("✅ ALL CHECKS PASSED")
        print("   The file should run without NameError issues")
        print("   Signal aggregation is properly implemented")
        print("   Ready for testing with proper Python environment")
        
        print(f"\n🚀 READY TO TEST:")
        print("   python simulate_trading_new.py --cfg strategyConfig_scalp_1s.json --start 2025-07-06 --end 2025-07-07")
        
        return True
    else:
        print("❌ SOME ISSUES REMAIN")
        if not syntax_ok:
            print("   • Syntax errors need to be fixed")
        if not variables_ok:
            print("   • Variable definition issues")
        if not fixes_ok:
            print("   • Some fixes are missing")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
