#!/usr/bin/env python3
"""
Test signal aggregation implementation without external dependencies.
"""

import json
import time
from datetime import datetime

class SignalAggregator:
    """Signal aggregation class to test the logic."""
    
    def __init__(self, window=5, confidence_threshold=0.65, min_trade_interval=30):
        self.window = window
        self.confidence_threshold = confidence_threshold
        self.min_trade_interval = min_trade_interval
        self.signal_history = []
        self.last_trade_time = 0
        self.action_ema = None
        self.ema_alpha = 0.3
        
    def process_signal(self, raw_signal, current_time=None):
        """Process raw signal through aggregation pipeline."""
        if current_time is None:
            current_time = time.time()
            
        # 1. EMA smoothing
        if self.action_ema is None:
            self.action_ema = raw_signal
        else:
            self.action_ema = self.ema_alpha * raw_signal + (1 - self.ema_alpha) * self.action_ema
        
        # 2. Add to signal history
        self.signal_history.append(self.action_ema)
        if len(self.signal_history) > self.window:
            self.signal_history.pop(0)
        
        # 3. Aggregate recent signals with weighted average
        if len(self.signal_history) >= 3:
            weights = [0.5, 0.3, 0.2]  # Last 3 signals
            aggregated_signal = sum(s * w for s, w in zip(self.signal_history[-3:], weights))
        else:
            aggregated_signal = self.action_ema
        
        # 4. Apply confidence filtering
        if abs(aggregated_signal) > self.confidence_threshold:
            final_signal = aggregated_signal
        else:
            final_signal = 0  # Ignore weak signals
        
        # 5. Time-based filtering
        if current_time - self.last_trade_time < self.min_trade_interval:
            if abs(final_signal) < 0.8:  # Only allow very strong signals during cooldown
                final_signal = 0
        
        return {
            'raw': raw_signal,
            'ema': self.action_ema,
            'aggregated': aggregated_signal,
            'final': final_signal,
            'history_length': len(self.signal_history)
        }
    
    def simulate_trade(self, signal, current_time):
        """Simulate a trade execution."""
        if abs(signal) > 0:
            self.last_trade_time = current_time
            return True
        return False

def test_signal_aggregation():
    """Test signal aggregation with realistic scenarios."""
    
    print("🧪 TESTING SIGNAL AGGREGATION")
    print("=" * 50)
    
    aggregator = SignalAggregator(
        window=5,
        confidence_threshold=0.65,
        min_trade_interval=30
    )
    
    # Test scenarios
    test_scenarios = [
        # Scenario 1: Oscillating signals (should be filtered)
        {
            'name': 'Oscillating Signals',
            'signals': [0.62, -0.58, 0.61, -0.59, 0.63, -0.60],
            'expected': 'Should filter most oscillations'
        },
        # Scenario 2: Consistent strong signals (should pass)
        {
            'name': 'Consistent Strong Signals',
            'signals': [0.75, 0.78, 0.82, 0.79, 0.81],
            'expected': 'Should generate trades'
        },
        # Scenario 3: Weak signals near threshold (should be filtered)
        {
            'name': 'Weak Signals Near Threshold',
            'signals': [0.55, 0.58, 0.52, 0.59, 0.56],
            'expected': 'Should be filtered as too weak'
        },
        # Scenario 4: Strong signal after weak ones (should pass)
        {
            'name': 'Strong Signal After Weak',
            'signals': [0.45, 0.52, 0.48, 0.85, 0.82],
            'expected': 'Should trigger on strong signals'
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n📊 Testing: {scenario['name']}")
        print(f"Expected: {scenario['expected']}")
        print("-" * 40)
        
        # Reset aggregator for each scenario
        aggregator = SignalAggregator()
        trades_executed = 0
        
        for i, signal in enumerate(scenario['signals']):
            current_time = time.time() + i * 10  # 10 seconds apart
            
            result = aggregator.process_signal(signal, current_time)
            
            # Check if trade would be executed
            if aggregator.simulate_trade(result['final'], current_time):
                trades_executed += 1
                trade_type = "LONG" if result['final'] > 0 else "SHORT"
                print(f"  ✅ TRADE {trades_executed}: {trade_type} signal={result['final']:.3f}")
            
            print(f"  Step {i+1}: raw={result['raw']:.3f} → ema={result['ema']:.3f} → agg={result['aggregated']:.3f} → final={result['final']:.3f}")
        
        print(f"  📈 Total trades: {trades_executed}")
        
        # Analysis
        if scenario['name'] == 'Oscillating Signals' and trades_executed <= 1:
            print("  ✅ SUCCESS: Oscillations filtered correctly")
        elif scenario['name'] == 'Consistent Strong Signals' and trades_executed >= 1:
            print("  ✅ SUCCESS: Strong signals passed through")
        elif scenario['name'] == 'Weak Signals Near Threshold' and trades_executed == 0:
            print("  ✅ SUCCESS: Weak signals filtered correctly")
        elif scenario['name'] == 'Strong Signal After Weak' and trades_executed >= 1:
            print("  ✅ SUCCESS: Strong signals detected after weak ones")
        else:
            print("  ⚠️  UNEXPECTED: Review aggregation logic")

def compare_with_without_aggregation():
    """Compare results with and without signal aggregation."""
    
    print(f"\n🔄 COMPARISON: With vs Without Aggregation")
    print("=" * 50)
    
    # Problematic signal sequence from real data
    problematic_signals = [
        -0.639906,  # SHORT near threshold
        0.620331,   # LONG near threshold (rapid reversal!)
        -0.588336,  # SHORT again
        0.498477,   # LONG again
        -0.530238,  # SHORT again
        0.482949,   # LONG again
    ]
    
    print("Real problematic sequence from threshold_breaches:")
    for i, sig in enumerate(problematic_signals):
        direction = "LONG" if sig > 0 else "SHORT"
        print(f"  {i+1}. {sig:.6f} ({direction})")
    
    # Test without aggregation (simple threshold)
    print(f"\n📊 WITHOUT Aggregation (simple threshold 0.6):")
    simple_trades = 0
    for i, sig in enumerate(problematic_signals):
        if abs(sig) > 0.6:
            simple_trades += 1
            direction = "LONG" if sig > 0 else "SHORT"
            print(f"  ✅ Trade {simple_trades}: {direction} @ {sig:.3f}")
        else:
            print(f"  ❌ Filtered: {sig:.3f}")
    print(f"  Total trades: {simple_trades}")
    
    # Test with aggregation
    print(f"\n📊 WITH Aggregation:")
    aggregator = SignalAggregator(confidence_threshold=0.65)
    agg_trades = 0
    
    for i, sig in enumerate(problematic_signals):
        current_time = time.time() + i * 5  # 5 seconds apart
        result = aggregator.process_signal(sig, current_time)
        
        if aggregator.simulate_trade(result['final'], current_time):
            agg_trades += 1
            direction = "LONG" if result['final'] > 0 else "SHORT"
            print(f"  ✅ Trade {agg_trades}: {direction} @ {result['final']:.3f}")
        else:
            print(f"  ❌ Filtered: raw={sig:.3f} → final={result['final']:.3f}")
    
    print(f"  Total trades: {agg_trades}")
    
    # Analysis
    print(f"\n📈 RESULTS:")
    print(f"  Without aggregation: {simple_trades} trades")
    print(f"  With aggregation: {agg_trades} trades")
    reduction = (simple_trades - agg_trades) / simple_trades * 100 if simple_trades > 0 else 0
    print(f"  Reduction: {reduction:.1f}%")
    
    if agg_trades < simple_trades:
        print("  ✅ SUCCESS: Aggregation reduced noisy trades")
    else:
        print("  ⚠️  WARNING: Aggregation may need tuning")

def create_aggregation_config():
    """Create configuration with optimized aggregation settings."""
    
    try:
        with open('strategyConfig_scalp_1s.json', 'r') as f:
            config = json.load(f)
        
        # Update with signal aggregation settings
        config['tradeParams']['entryActionThreshold'] = 0.5  # Lower base threshold
        config['tradeParams']['longEntryThreshold'] = 0.5
        config['tradeParams']['shortEntryThreshold'] = 0.5
        config['tradeParams']['exitActionThreshold'] = 0.4
        
        # Add signal aggregation configuration
        config['signalAggregation'] = {
            "enabled": True,
            "emaAlpha": 0.3,
            "aggregationWindow": 5,
            "confidenceThreshold": 0.65,
            "minTradeInterval": 30,
            "strongSignalOverride": 0.8,
            "description": "Signal aggregation to reduce noise and improve stability"
        }
        
        # Save aggregation config
        output_file = 'strategyConfig_scalp_1s_aggregated.json'
        with open(output_file, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"✅ Created aggregation config: {output_file}")
        
        print(f"\n📋 AGGREGATION SETTINGS:")
        print(f"  Base threshold: 0.5 (lower to catch more signals)")
        print(f"  Confidence threshold: 0.65 (higher for final decision)")
        print(f"  Aggregation window: 5 signals")
        print(f"  Min trade interval: 30 seconds")
        print(f"  Strong signal override: 0.8 (bypass cooldown)")
        
        return output_file
        
    except Exception as e:
        print(f"❌ Error creating aggregation config: {e}")
        return None

def main():
    """Main test function."""
    
    print("🔧 SIGNAL AGGREGATION - COMPREHENSIVE TEST")
    print("=" * 60)
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test signal aggregation logic
    test_signal_aggregation()
    
    # Compare with/without aggregation
    compare_with_without_aggregation()
    
    # Create optimized configuration
    config_file = create_aggregation_config()
    
    print(f"\n🎯 SUMMARY:")
    print("=" * 30)
    print("✅ Signal aggregation logic tested")
    print("✅ Comparison with simple threshold completed")
    print("✅ Optimized configuration created")
    
    if config_file:
        print(f"\n🚀 NEXT STEPS:")
        print(f"1. Test with aggregation config:")
        print(f"   python simulate_trading_new.py --config {config_file}")
        print(f"2. Compare threshold breach patterns")
        print(f"3. Monitor trade quality improvement")
        print(f"4. Adjust aggregation parameters if needed")
    
    print(f"\n💡 EXPECTED IMPROVEMENTS:")
    print("• 30-50% reduction in noisy trades")
    print("• More stable signal direction")
    print("• Better win rate due to higher quality entries")
    print("• Reduced whipsaw trades")

if __name__ == "__main__":
    main()
