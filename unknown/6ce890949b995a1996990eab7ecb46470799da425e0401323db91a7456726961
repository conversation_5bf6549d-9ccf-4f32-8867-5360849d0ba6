# Threshold Logging Feature

## Overview

The `simulate_trading_new.py` script now includes comprehensive threshold logging functionality that captures all threshold breaches during trading simulation. This feature helps analyze when entry and exit signals exceed the configured thresholds.

## What Gets Logged

The threshold logging captures:

1. **Entry Signal Thresholds**:
   - Long entry signals that exceed `longEntryThreshold`
   - Short entry signals that fall below `-shortEntryThreshold`
   - Near-miss signals (within 80% of threshold)

2. **Exit Signal Thresholds**:
   - Long exit signals that exceed `exitActionThreshold`
   - Short exit signals that fall below `-exitActionThreshold`
   - Near-miss exit signals (within 80% of threshold)

## Log File Format

When you run the simulation, a timestamped log file is automatically created:
```
threshold_breaches_YYYYMMDD_HHMMSS.txt
```

### Log File Structure

```
=== THRESHOLD BREACH LOG ===
Generated: 2025-07-12 01:00:49
Config: XRPUSDC - 1s
Long Entry Threshold: 0.99
Short Entry Threshold: 0.99
Exit Threshold: 0.99
================================================================================
Format: TIMESTAMP | SIGNAL_VALUE | COMPARISON | THRESHOLD | SIGNAL_TYPE | RESULT
================================================================================

2025-01-04T00:00:01Z | 0.995000 | > | 0.990000 | LONG_ENTRY | TRIGGERED
2025-01-04T00:00:02Z | -0.995000 | < | -0.990000 | SHORT_ENTRY | TRIGGERED
2025-01-04T00:00:03Z | 0.800000 | > | 0.990000 | LONG_ENTRY | NEAR_MISS
...

================================================================================
=== FINAL SUMMARY ===
Total signals processed: 1000
Long threshold breaches: 25
Short threshold breaches: 18
Total threshold breaches: 43
Breach rate: 4.30%
Signal statistics - Mean: 0.001234, Std: 0.456789
Signal range: [-2.345678, 2.123456]
================================================================================
```

## Log Entry Fields

Each log entry contains:
- **TIMESTAMP**: When the signal occurred
- **SIGNAL_VALUE**: The actual signal value from the agent
- **COMPARISON**: The comparison operator (> or <)
- **THRESHOLD**: The threshold value being compared against
- **SIGNAL_TYPE**: Type of signal (LONG_ENTRY, SHORT_ENTRY, LONG_EXIT, SHORT_EXIT)
- **RESULT**: Whether it was TRIGGERED or NEAR_MISS

## Usage

Simply run your simulation as usual:

```bash
python3 simulate_trading_new.py strategyConfig_scalp_1s.json \
    --start-time "2025-01-04T00:00:00Z" \
    --end-time "2025-01-04T23:59:59Z" \
    --out-trades trades.csv \
    --out-equity equity.csv
```

The threshold log file will be automatically created in the same directory.

## Configuration

The thresholds are read from your configuration file:

```json
{
  "tradeParams": {
    "entryActionThreshold": 0.99,
    "longEntryThreshold": 0.99,
    "shortEntryThreshold": 0.99,
    "exitActionThreshold": 0.99
  }
}
```

## Analysis Tips

1. **Low Breach Rate (<1%)**: Thresholds might be too high, preventing trades
2. **High Breach Rate (>50%)**: Thresholds might be too low, causing excessive trading
3. **Near-Miss Analysis**: Look for patterns in signals that almost triggered
4. **Signal Distribution**: Check if signals are normally distributed around zero
5. **Temporal Patterns**: Analyze if threshold breaches cluster at certain times

## Benefits

- **Debugging**: Identify why expected trades didn't trigger
- **Optimization**: Fine-tune thresholds based on signal distribution
- **Validation**: Ensure agent signals are behaving as expected
- **Analysis**: Understand signal patterns and market conditions

## Files Generated

- `threshold_breaches_YYYYMMDD_HHMMSS.txt`: Main threshold log file
- Console output also shows threshold breach summary statistics

This feature provides detailed insight into your trading agent's decision-making process and helps optimize threshold parameters for better performance.
