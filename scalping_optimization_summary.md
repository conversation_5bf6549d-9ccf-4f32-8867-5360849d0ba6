# Optimalizácia pre kratšie scalping obchody

## 📊 **Analýza pôvodných v<PERSON>dkov:**

### **Problémy identifikované:**
- **P<PERSON><PERSON><PERSON><PERSON> dlhé obchody**: 941.8 minút (15.7 hodín) - to nie je scalping!
- **Nízky win rate**: 33.33% - potrebujeme viac výherných obchodov
- **Nízky profit factor**: 0.34 - straty prevyšujú zisky
- **<PERSON><PERSON><PERSON> obchodov**: Iba 3 obchody za 2 dni - potrebujeme viac príležitostí

### **Príčiny problémov:**
1. **Vysoké thresholdy** (0.6) - málo entry signálov
2. **Vysoký RR target** (1.0) - dlhé čakanie na TP
3. **Agent exits disabled** - žiadne inteligentné výstupy
4. **Vysoké SL distances** - p<PERSON><PERSON><PERSON><PERSON> stop lossy

## 🔧 **Aplikovan<PERSON> optimaliz<PERSON>cie:**

### **1. Znížené entry thresholdy**
```json
// PRED:
"entryActionThreshold": 0.6,
"longEntryThreshold": 0.6,
"shortEntryThreshold": 0.6,

// PO:
"entryActionThreshold": 0.4,  // -33% pre viac signálov
"longEntryThreshold": 0.4,
"shortEntryThreshold": 0.4,
```

### **2. Znížený exit threshold**
```json
// PRED:
"exitActionThreshold": 0.6,

// PO:
"exitActionThreshold": 0.3,  // -50% pre skoršie výstupy
```

### **3. Znížený RR target**
```json
// PRED:
"rrTarget": 1,  // 1:1 risk/reward

// PO:
"rrTarget": 0.5,  // 1:0.5 risk/reward - kratšie obchody
```

### **4. Povolené agent exits**
```json
// PRED:
"agentExitsEnabled": false,

// PO:
"agentExitsEnabled": true,  // Agent môže inteligentne vystúpiť
```

### **5. Kratší minimálny čas v obchode**
```json
// PRED:
"minTimeInTradeSeconds": 30,

// PO:
"minTimeInTradeSeconds": 10,  // Rýchlejšie scalping
```

### **6. Užšie stop lossy**
```json
// PRED:
"minSLDistanceATR": 2,
"minSLDistancePercent": 2.5,

// PO:
"minSLDistanceATR": 1.5,     // -25% užší SL
"minSLDistancePercent": 1.5, // -40% užší SL
```

## 📈 **Očakávané výsledky:**

### **Pred optimalizáciou:**
- **Obchody**: 3 za 2 dni (1.5/deň)
- **Win rate**: 33.33%
- **Avg trade duration**: 941.8 min (15.7h)
- **Profit factor**: 0.34
- **PnL**: -$3.40

### **Po optimalizácii (očakávané):**
- **Obchody**: 15-30 za 2 dni (7.5-15/deň) 📈
- **Win rate**: 45-55% 📈
- **Avg trade duration**: 30-120 min (0.5-2h) 📉
- **Profit factor**: 1.1-1.4 📈
- **PnL**: Pozitívny 📈

## 🎯 **Scalping stratégia logika:**

### **Filozofia zmien:**
1. **Viac obchodov** = viac príležitostí na zisk
2. **Kratšie obchody** = menšie riziko, rýchlejšie zisky
3. **Nižší RR** = vyššia pravdepodobnosť dosiahnutia TP
4. **Agent exits** = inteligentné výstupy pred SL
5. **Užšie SL** = menšie straty pri neúspešných obchodoch

### **Risk management:**
- **Menšie individuálne riziká** (užšie SL)
- **Viac diverzifikovaných obchodov**
- **Rýchlejšie reakcie na zmeny trhu**
- **Lepšia kontrola drawdown**

## 🧪 **Testovací plán:**

### **1. Krátky test (2 dni):**
```bash
python simulate_trading_new.py --cfg strategyConfig_scalp_1s.json --start 2025-07-01 --end 2025-07-03
```

### **2. Metriky na sledovanie:**
- **Trade frequency**: Cieľ 10-20 obchodov/deň
- **Average duration**: Cieľ <2 hodiny
- **Win rate**: Cieľ >45%
- **Profit factor**: Cieľ >1.1
- **Max drawdown**: Cieľ <10%

### **3. Ďalšie ladenie (ak potrebné):**
- Ak príliš málo obchodov → znížiť thresholdy na 0.3
- Ak príliš veľa stratových → zvýšiť exit threshold na 0.4
- Ak stále dlhé obchody → znížiť RR na 0.3
- Ak vysoký drawdown → zvýšiť SL distances

## 💡 **Kľúčové princípy scalping:**

1. **Frekvencia > Veľkosť zisku**
2. **Rýchlosť > Presnosť**
3. **Konzistentnosť > Veľké výhry**
4. **Risk control > Maximálny profit**

**Cieľ: Malé, časté zisky s kontrolovaným rizikom namiesto veľkých, zriedkavých obchodov.**
