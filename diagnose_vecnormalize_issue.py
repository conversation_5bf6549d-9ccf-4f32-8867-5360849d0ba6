#!/usr/bin/env python3
"""
Diagnostic script to identify and fix VecNormalize issues causing low signal variability.
"""

import json
from datetime import datetime
from pathlib import Path

def analyze_config_differences():
    """Compare configurations to identify potential issues."""
    
    print("🔍 VECNORMALIZE ISSUE DIAGNOSTICS")
    print("=" * 50)
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    configs_to_check = [
        'strategyConfig_scalp_1s.json',
        'strategyConfig_scalp_1s_no_vecnorm.json'
    ]
    
    print(f"\n📋 CONFIGURATION COMPARISON:")
    print("-" * 40)
    
    for config_file in configs_to_check:
        if Path(config_file).exists():
            try:
                with open(config_file, 'r') as f:
                    config = json.load(f)
                
                vecnorm_disabled = config.get('_vecnormalize_disabled', False)
                entry_threshold = config['tradeParams']['entryActionThreshold']
                rr_target = config['tradeParams']['rrTarget']
                agent_exits = config['tradeParams']['agentExitsEnabled']
                
                print(f"\n{config_file}:")
                print(f"  VecNormalize disabled: {vecnorm_disabled}")
                print(f"  Entry threshold: {entry_threshold}")
                print(f"  RR target: {rr_target}")
                print(f"  Agent exits: {agent_exits}")
                
            except Exception as e:
                print(f"  ❌ Error reading {config_file}: {e}")
        else:
            print(f"  ⚠️  {config_file} not found")

def analyze_threshold_logs():
    """Analyze recent threshold logs to identify patterns."""
    
    print(f"\n📊 THRESHOLD LOG ANALYSIS:")
    print("-" * 30)
    
    # Find recent threshold files
    threshold_files = list(Path('.').glob('threshold_breaches_*.txt'))
    threshold_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
    
    if not threshold_files:
        print("  ⚠️  No threshold breach files found")
        return
    
    # Analyze most recent file
    recent_file = threshold_files[0]
    print(f"  Analyzing: {recent_file}")
    
    try:
        with open(recent_file, 'r') as f:
            lines = f.readlines()
        
        # Count different types of entries
        triggered_count = 0
        near_miss_count = 0
        signal_values = []
        
        for line in lines:
            if '|' in line and ('TRIGGERED' in line or 'NEAR_MISS' in line):
                parts = line.strip().split('|')
                if len(parts) >= 6:
                    try:
                        signal_value = float(parts[1].strip())
                        result = parts[5].strip()
                        
                        signal_values.append(signal_value)
                        
                        if result == 'TRIGGERED':
                            triggered_count += 1
                        elif result == 'NEAR_MISS':
                            near_miss_count += 1
                    except:
                        continue
        
        total_signals = triggered_count + near_miss_count
        
        print(f"  Total signals: {total_signals}")
        print(f"  Triggered: {triggered_count}")
        print(f"  Near miss: {near_miss_count}")
        
        if signal_values:
            import statistics
            signal_mean = statistics.mean(signal_values)
            signal_std = statistics.stdev(signal_values) if len(signal_values) > 1 else 0
            signal_min = min(signal_values)
            signal_max = max(signal_values)
            
            print(f"  Signal stats: mean={signal_mean:.4f}, std={signal_std:.4f}")
            print(f"  Signal range: [{signal_min:.4f}, {signal_max:.4f}]")
            
            # Diagnosis
            if signal_std < 0.1:
                print(f"  🚨 LOW VARIABILITY DETECTED (std={signal_std:.4f})")
                print(f"     This confirms the VecNormalize issue!")
            
            if total_signals < 10:
                print(f"  ⚠️  Very few signals ({total_signals}) - agent may be 'stuck'")
        
    except Exception as e:
        print(f"  ❌ Error analyzing {recent_file}: {e}")

def create_diagnostic_summary():
    """Create a summary of the issue and solutions."""
    
    print(f"\n🎯 DIAGNOSTIC SUMMARY:")
    print("=" * 30)
    
    print("🔍 IDENTIFIED ISSUES:")
    issues = [
        "Agent generates signals with std=0.0000 (almost identical)",
        "Very few threshold breaches despite low thresholds",
        "VecNormalize may be causing feature normalization problems",
        "Model may be 'stuck' due to distribution shift"
    ]
    
    for issue in issues:
        print(f"  • {issue}")
    
    print(f"\n💡 POTENTIAL CAUSES:")
    causes = [
        "VecNormalize statistics from training don't match current data",
        "Feature distribution has shifted since training",
        "VecNormalize is over-normalizing features to near-zero variance",
        "Model was trained on different data distribution",
        "Orderbook features have different scaling than training data"
    ]
    
    for cause in causes:
        print(f"  • {cause}")
    
    print(f"\n🔧 SOLUTIONS TO TRY:")
    solutions = [
        "1. Test without VecNormalize (use strategyConfig_scalp_1s_no_vecnorm.json)",
        "2. Lower thresholds even more (0.1-0.2) to catch any signals",
        "3. Check if model file and VecNormalize file are compatible",
        "4. Use different model checkpoint",
        "5. Retrain model on current data distribution"
    ]
    
    for solution in solutions:
        print(f"  {solution}")
    
    print(f"\n🧪 IMMEDIATE TESTING PLAN:")
    tests = [
        "1. Run with VecNormalize disabled:",
        "   python simulate_trading_new.py --cfg strategyConfig_scalp_1s_no_vecnorm.json --start 2025-07-01 --end 2025-07-02",
        "",
        "2. Compare signal variability in threshold logs",
        "",
        "3. If signals improve without VecNormalize:",
        "   - Problem is confirmed to be VecNormalize",
        "   - Either fix VecNormalize or train new model without it",
        "",
        "4. If signals still have low variability:",
        "   - Problem is with model or features",
        "   - Try different model checkpoint or retrain"
    ]
    
    for test in tests:
        print(f"  {test}")

def check_model_files():
    """Check available model and VecNormalize files."""
    
    print(f"\n📁 MODEL FILES CHECK:")
    print("-" * 25)
    
    # Check for model files
    model_patterns = ['*.zip', 'sac_*_steps*', '*.pkl']
    model_files = []
    
    for pattern in model_patterns:
        model_files.extend(list(Path('.').glob(pattern)))
    
    print("Available model files:")
    for model_file in sorted(model_files):
        size_mb = model_file.stat().st_size / (1024 * 1024)
        print(f"  {model_file} ({size_mb:.1f} MB)")
    
    # Check for VecNormalize files
    vecnorm_files = list(Path('.').glob('*vecnorm*.pkl'))
    
    print(f"\nAvailable VecNormalize files:")
    if vecnorm_files:
        for vecnorm_file in sorted(vecnorm_files):
            size_kb = vecnorm_file.stat().st_size / 1024
            print(f"  {vecnorm_file} ({size_kb:.1f} KB)")
    else:
        print("  ⚠️  No VecNormalize files found")
    
    # Check compatibility
    if model_files and vecnorm_files:
        print(f"\n🔗 COMPATIBILITY CHECK:")
        print("  Model and VecNormalize files found")
        print("  ⚠️  Cannot verify compatibility without loading")
        print("  💡 Try running without VecNormalize to isolate the issue")

def main():
    """Main diagnostic function."""
    
    # Run all diagnostics
    analyze_config_differences()
    analyze_threshold_logs()
    check_model_files()
    create_diagnostic_summary()
    
    print(f"\n" + "="*60)
    print("🎯 CONCLUSION")
    print("="*60)
    print("The low signal variability (std=0.0000) indicates VecNormalize issues.")
    print("Test with VecNormalize disabled to confirm the diagnosis.")
    print("If signals improve, the problem is VecNormalize compatibility.")
    print("If signals remain low-variance, the problem is with the model itself.")

if __name__ == "__main__":
    main()
