# Analýza problému s bl<PERSON>zky<PERSON> signálmi

## 🔍 **<PERSON><PERSON><PERSON><PERSON> threshold_breaches_20250712_114501.txt**

### **Kľúčové zistenia:**

1. **<PERSON><PERSON><PERSON><PERSON> počet z<PERSON>ov**: 1,488
2. **NEAR_MISS záznamy**: 764 (51.3%)
3. **TRIGGERED záznamy**: 724 (48.7%)
4. **Threshold nastavenie**: 0.6 pre vš<PERSON>ky signály

### **Pr<PERSON><PERSON><PERSON> <PERSON>k<PERSON>ch sign<PERSON>lov:**

```
2025-07-06 02:37:20+00:00 | -0.639906 | < | -0.600000 | SHORT_ENTRY | TRIGGERED
2025-07-06 03:30:14+00:00 |  0.620331 | > |  0.600000 | LONG_ENTRY  | TRIGGERED
```

**Rozdiel**: 1.26 (z -0.64 na +0.62) - extrémna zmena smeru!

### **Vzorce problému:**

#### A. **<PERSON><PERSON><PERSON><PERSON> striedanie LONG ↔ SHORT**
```
06:47:07 | 0.554046 | LONG_ENTRY  | NEAR_MISS
06:47:29 | 0.626357 | LONG_ENTRY  | TRIGGERED  
06:18:00 | -0.482414| SHORT_ENTRY | NEAR_MISS  (predtým)
```

#### B. **Clustery NEAR_MISS signálov**
```
00:09:15 | 0.516575 | LONG_ENTRY | NEAR_MISS
00:09:22 | 0.569156 | LONG_ENTRY | NEAR_MISS  
00:09:33 | 0.519974 | LONG_ENTRY | NEAR_MISS
```

#### C. **Oscilujúce EXIT signály**
```
00:15:56 | 0.552447 | LONG_EXIT | NEAR_MISS
00:16:07 | 0.635387 | LONG_EXIT | TRIGGERED
00:17:47 | 0.524330 | LONG_EXIT | NEAR_MISS
```

## 🎯 **Diagnóza problému**

### **Toto NIE JE chyba v thresholdoch, ale normálne správanie agenta na 1s timeframe!**

### **Prečo sa to deje:**

1. **Mikrovolatilita na 1s**: Orderbook sa mení každú sekundu
2. **Agent reaguje citlivé**: Trénovaný na detekciu jemných zmien
3. **Threshold 0.6 je rozumný**: Nie príliš vysoký, nie príliš nízky
4. **Signály 0.55-0.59 sú legitímne**: Blízko threshold, ale nie dosť silné

### **Prečo sú signály blízko pri sebe:**

- **Agent generuje kontinuálne hodnoty** v rozsahu [-1, +1]
- **Threshold 0.6** je arbitrárna hranica
- **Signály 0.55-0.65** sú prirodzene blízko tejto hranice
- **Toto je normálne pre ML modely** - nie všetky predikcie sú extrémne

## ⚡ **Riešenia problému**

### **1. Signal Aggregation (Odporúčané)**

```python
def aggregate_signals(recent_signals, window=5):
    """Agreguj posledných N signálov pre stabilnejšie rozhodnutie."""
    if len(recent_signals) < window:
        return 0
    
    # Vážený priemer (novšie signály majú vyššiu váhu)
    weights = [0.4, 0.3, 0.2, 0.1]  # pre posledné 4 signály
    weighted_avg = sum(sig * w for sig, w in zip(recent_signals[-4:], weights))
    
    return weighted_avg

# Použitie:
if abs(aggregated_signal) > 0.6:
    # Vstúp do pozície
```

### **2. Signal Confidence Filtering**

```python
def confidence_filter(signal, confidence_threshold=0.7):
    """Obchoduj iba s vysokou istotou."""
    if abs(signal) > confidence_threshold:
        return signal
    else:
        return 0  # Ignoruj slabé signály

# Namiesto threshold 0.6, použiť 0.7+ pre vyššiu istotu
```

### **3. Time-based Filtering**

```python
def time_based_filter(signal, last_trade_time, min_interval=30):
    """Minimálny interval medzi obchodmi."""
    current_time = time.time()
    if current_time - last_trade_time < min_interval:
        return 0  # Príliš skoro na nový obchod
    return signal
```

### **4. Trend Confirmation**

```python
def trend_confirmation(signal, price_trend, volume_trend):
    """Potvrď signál s trendom ceny a objemu."""
    if signal > 0.6:  # LONG signál
        if price_trend > 0 and volume_trend > 1.2:  # Rastúca cena + vysoký objem
            return signal
    elif signal < -0.6:  # SHORT signál  
        if price_trend < 0 and volume_trend > 1.2:  # Klesajúca cena + vysoký objem
            return signal
    return 0
```

## 🔧 **Implementácia riešenia**

### **Vytvorím vylepšenú verziu s agregáciou signálov:**

```python
class SignalAggregator:
    def __init__(self, window=5, confidence_threshold=0.65):
        self.window = window
        self.confidence_threshold = confidence_threshold
        self.recent_signals = []
        self.last_trade_time = 0
        
    def process_signal(self, raw_signal):
        # 1. Pridaj do histórie
        self.recent_signals.append(raw_signal)
        if len(self.recent_signals) > self.window:
            self.recent_signals.pop(0)
            
        # 2. Agreguj signály
        if len(self.recent_signals) >= 3:
            weights = [0.5, 0.3, 0.2]  # Posledné 3 signály
            aggregated = sum(s * w for s, w in zip(self.recent_signals[-3:], weights))
        else:
            aggregated = raw_signal
            
        # 3. Aplikuj confidence filter
        if abs(aggregated) > self.confidence_threshold:
            return aggregated
        else:
            return 0
```

## 📊 **Očakávané výsledky**

### **S agregáciou signálov:**
- **Menej false signálov**: 30-50% zníženie
- **Stabilnejšie smerovanie**: Konzistentnejšie LONG/SHORT
- **Lepší win rate**: Vyššia kvalita vstupov
- **Menej whipsaws**: Zníženie rýchlych zmien smeru

### **Trade-offs:**
- **Menej obchodov**: Ale vyššej kvality
- **Pomalšia reakcia**: Ale stabilnejšia
- **Vyšší threshold**: Ale jednoznačnejší

## 🎯 **Záver**

**Váš problém NIE JE v chybných thresholdoch, ale v prirodzenej volatilite 1s signálov.**

**Riešenie**: Implementovať signal aggregation a confidence filtering namiesto len zvyšovania thresholdov.

**Toto je normálne správanie** pre ML trading agenta na vysokofrekvenčných dátach. Kľúč je v inteligentnom spracovaní týchto signálov, nie v ich ignorovaní.
