#!/usr/bin/env python3
"""
Test script to find optimal thresholds for the trading agent.
This script will test different threshold combinations and analyze their performance.
"""

import json
import subprocess
import pandas as pd
from pathlib import Path
import logging
from datetime import datetime
import numpy as np

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)

def create_test_config(base_config_path, threshold_values, output_path):
    """Create a test configuration with specific threshold values."""
    with open(base_config_path, 'r') as f:
        config = json.load(f)
    
    # Update thresholds
    config['tradeParams']['entryActionThreshold'] = threshold_values['entry']
    config['tradeParams']['longEntryThreshold'] = threshold_values['long_entry']
    config['tradeParams']['shortEntryThreshold'] = threshold_values['short_entry']
    config['tradeParams']['exitActionThreshold'] = threshold_values['exit']
    
    # Save test config
    with open(output_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    return output_path

def run_simulation(config_path, test_name):
    """Run trading simulation with given config."""
    log.info(f"Running simulation: {test_name}")
    
    try:
        # Run simulation
        result = subprocess.run([
            'python', 'simulate_trading_new.py', 
            '--config', str(config_path)
        ], capture_output=True, text=True, timeout=300)  # 5 minute timeout
        
        if result.returncode != 0:
            log.error(f"Simulation failed for {test_name}: {result.stderr}")
            return None
            
        # Parse results from stdout/logs
        return parse_simulation_results(result.stdout, test_name)
        
    except subprocess.TimeoutExpired:
        log.error(f"Simulation timeout for {test_name}")
        return None
    except Exception as e:
        log.error(f"Error running simulation {test_name}: {e}")
        return None

def parse_simulation_results(output, test_name):
    """Parse simulation results from output."""
    results = {
        'test_name': test_name,
        'total_trades': 0,
        'profitable_trades': 0,
        'total_pnl': 0.0,
        'win_rate': 0.0,
        'profit_factor': 0.0,
        'max_drawdown': 0.0,
        'avg_trade_duration': 0.0
    }
    
    # Parse key metrics from output
    lines = output.split('\n')
    for line in lines:
        if 'Total trades:' in line:
            try:
                results['total_trades'] = int(line.split(':')[1].strip())
            except:
                pass
        elif 'Win rate:' in line:
            try:
                results['win_rate'] = float(line.split(':')[1].strip().replace('%', ''))
            except:
                pass
        elif 'Total PnL:' in line:
            try:
                results['total_pnl'] = float(line.split(':')[1].strip().replace('$', ''))
            except:
                pass
        elif 'Profit factor:' in line:
            try:
                results['profit_factor'] = float(line.split(':')[1].strip())
            except:
                pass
    
    return results

def main():
    """Main function to test different threshold combinations."""
    
    # Test configurations
    threshold_tests = [
        {
            'name': 'conservative',
            'entry': 0.7,
            'long_entry': 0.7,
            'short_entry': 0.7,
            'exit': 0.6
        },
        {
            'name': 'moderate',
            'entry': 0.5,
            'long_entry': 0.5,
            'short_entry': 0.5,
            'exit': 0.4
        },
        {
            'name': 'aggressive',
            'entry': 0.3,
            'long_entry': 0.3,
            'short_entry': 0.3,
            'exit': 0.25
        },
        {
            'name': 'very_aggressive',
            'entry': 0.1,
            'long_entry': 0.1,
            'short_entry': 0.1,
            'exit': 0.1
        },
        {
            'name': 'asymmetric',
            'entry': 0.4,
            'long_entry': 0.35,  # Easier long entries
            'short_entry': 0.45,  # Harder short entries
            'exit': 0.3
        }
    ]
    
    base_config = 'strategyConfig_scalp_1s.json'
    results = []
    
    log.info("Starting threshold optimization tests...")
    
    for test in threshold_tests:
        log.info(f"\n{'='*50}")
        log.info(f"Testing configuration: {test['name']}")
        log.info(f"Thresholds: Entry={test['entry']}, Long={test['long_entry']}, Short={test['short_entry']}, Exit={test['exit']}")
        
        # Create test config
        test_config_path = f"test_config_{test['name']}.json"
        create_test_config(base_config, test, test_config_path)
        
        # Run simulation
        result = run_simulation(test_config_path, test['name'])
        
        if result:
            results.append(result)
            log.info(f"Results: Trades={result['total_trades']}, Win Rate={result['win_rate']:.1f}%, PnL=${result['total_pnl']:.2f}")
        else:
            log.warning(f"No results for {test['name']}")
        
        # Clean up test config
        try:
            Path(test_config_path).unlink()
        except:
            pass
    
    # Analyze results
    if results:
        log.info(f"\n{'='*60}")
        log.info("THRESHOLD OPTIMIZATION RESULTS")
        log.info(f"{'='*60}")
        
        # Create results DataFrame
        df_results = pd.DataFrame(results)
        
        # Sort by profit factor
        df_results = df_results.sort_values('profit_factor', ascending=False)
        
        log.info("\nRanked by Profit Factor:")
        for _, row in df_results.iterrows():
            log.info(f"{row['test_name']:15} | Trades: {row['total_trades']:3d} | Win Rate: {row['win_rate']:5.1f}% | PnL: ${row['total_pnl']:8.2f} | PF: {row['profit_factor']:5.2f}")
        
        # Save detailed results
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_file = f"threshold_optimization_results_{timestamp}.csv"
        df_results.to_csv(results_file, index=False)
        log.info(f"\nDetailed results saved to: {results_file}")
        
        # Recommendations
        best_config = df_results.iloc[0]
        log.info(f"\n🏆 BEST CONFIGURATION: {best_config['test_name']}")
        log.info(f"   Recommended for production use")
        log.info(f"   Expected performance: {best_config['total_trades']} trades, {best_config['win_rate']:.1f}% win rate, ${best_config['total_pnl']:.2f} PnL")
        
    else:
        log.error("No successful test results obtained!")

if __name__ == "__main__":
    main()
