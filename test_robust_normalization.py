#!/usr/bin/env python3
"""
Test script to compare old vs new robust orderbook normalization.
This will help us see if the robust normalization reduces signal oscillation.
"""

import numpy as np
import json
from datetime import datetime
import sys
import os

# Add current directory to path to import from simulate_trading_new
sys.path.append(os.getcwd())

def generate_synthetic_orderbook_data(n_samples=1000):
    """Generate synthetic orderbook data with realistic volatility patterns."""
    
    # Simulate realistic orderbook volume patterns
    np.random.seed(42)  # For reproducible results
    
    data = {}
    
    # Base volumes with different characteristics for each level
    for level in range(1, 6):
        # Level 1 (closest to mid) - most volatile
        if level == 1:
            base_vol = np.random.lognormal(mean=5, sigma=1.5, size=n_samples)
            # Add sudden spikes (market orders)
            spike_mask = np.random.random(n_samples) < 0.05  # 5% chance of spike
            spikes = np.random.lognormal(mean=8, sigma=1, size=n_samples)
            base_vol = np.where(spike_mask, spikes, base_vol)
        
        # Level 2-3 - moderate volatility
        elif level <= 3:
            base_vol = np.random.lognormal(mean=6, sigma=1.2, size=n_samples)
            spike_mask = np.random.random(n_samples) < 0.02  # 2% chance of spike
            spikes = np.random.lognormal(mean=7.5, sigma=0.8, size=n_samples)
            base_vol = np.where(spike_mask, spikes, base_vol)
        
        # Level 4-5 - more stable
        else:
            base_vol = np.random.lognormal(mean=6.5, sigma=1.0, size=n_samples)
            spike_mask = np.random.random(n_samples) < 0.01  # 1% chance of spike
            spikes = np.random.lognormal(mean=7, sigma=0.6, size=n_samples)
            base_vol = np.where(spike_mask, spikes, base_vol)
        
        # Add some correlation between bid and ask
        bid_vol = base_vol * np.random.uniform(0.8, 1.2, n_samples)
        ask_vol = base_vol * np.random.uniform(0.8, 1.2, n_samples)
        
        data[f'ob_bid_vol_l{level}'] = bid_vol
        data[f'ob_ask_vol_l{level}'] = ask_vol
    
    # Add some other features for context
    data['close'] = 100 + np.cumsum(np.random.normal(0, 0.1, n_samples))
    data['volume'] = np.random.lognormal(mean=10, sigma=0.5, size=n_samples)
    
    return data

def test_normalization_methods():
    """Test and compare different normalization approaches."""
    
    print("🧪 TESTING ROBUST ORDERBOOK NORMALIZATION")
    print("=" * 60)
    
    # Generate test data
    print("1. Generating synthetic orderbook data...")
    data = generate_synthetic_orderbook_data(1000)
    
    # Create a simple DataFrame-like structure
    class SimpleDataFrame:
        def __init__(self, data_dict):
            self.data = data_dict
            self.columns = list(data_dict.keys())
        
        def copy(self):
            return SimpleDataFrame({k: v.copy() for k, v in self.data.items()})
        
        def __getitem__(self, key):
            if isinstance(key, list):
                return SimpleDataFrame({k: self.data[k] for k in key if k in self.data})
            return self.data[key]
        
        def __setitem__(self, key, value):
            self.data[key] = value
        
        def empty(self):
            return len(self.data) == 0
    
    df = SimpleDataFrame(data)
    
    # Test old normalization (basic z-score)
    print("\n2. Testing BASIC normalization...")
    
    ob_vol_cols = [col for col in df.columns if col.startswith(('ob_bid_vol', 'ob_ask_vol'))]
    print(f"   Found {len(ob_vol_cols)} orderbook volume columns")
    
    # Basic normalization stats
    basic_stats = {}
    for col in ob_vol_cols:
        values = df[col]
        mean_val = np.mean(values)
        std_val = np.std(values)
        
        # Basic z-score normalization
        normalized = (values - mean_val) / std_val
        normalized = np.clip(normalized, -5.0, 5.0)
        
        basic_stats[col] = {
            'original_mean': mean_val,
            'original_std': std_val,
            'normalized_mean': np.mean(normalized),
            'normalized_std': np.std(normalized),
            'normalized_range': [np.min(normalized), np.max(normalized)],
            'extreme_count': np.sum(np.abs(normalized) > 3.0),
            'extreme_pct': np.sum(np.abs(normalized) > 3.0) / len(normalized) * 100
        }
    
    print("   Basic normalization results:")
    for col in ob_vol_cols[:3]:
        stats = basic_stats[col]
        print(f"     {col}: extreme_values={stats['extreme_count']} ({stats['extreme_pct']:.1f}%), range={stats['normalized_range']}")
    
    # Test robust normalization
    print("\n3. Testing ROBUST normalization...")
    
    try:
        # Import the robust normalization function
        from simulate_trading_new import apply_robust_orderbook_normalization
        
        df_robust, robust_stats = apply_robust_orderbook_normalization(df, alpha=0.1)
        
        print("   Robust normalization results:")
        for col in ob_vol_cols[:3]:
            if col in robust_stats:
                stats = robust_stats[col]
                normalized_values = df_robust[col]
                extreme_count = np.sum(np.abs(normalized_values) > 3.0)
                extreme_pct = extreme_count / len(normalized_values) * 100
                
                print(f"     {col}: stability={stats.get('stability_factor', 1.0):.3f}, extreme_values={extreme_count} ({extreme_pct:.1f}%)")
                print(f"       median={stats['median']:.4f}, mad={stats['mad']:.4f}")
                print(f"       range=[{np.min(normalized_values):.2f}, {np.max(normalized_values):.2f}]")
        
        # Compare oscillation potential
        print("\n4. OSCILLATION ANALYSIS:")
        print("   Comparing potential for signal oscillation...")
        
        for col in ob_vol_cols[:2]:  # Test first 2 columns
            # Basic normalization
            basic_values = df[col]
            basic_normalized = (basic_values - np.mean(basic_values)) / np.std(basic_values)
            basic_normalized = np.clip(basic_normalized, -5.0, 5.0)
            
            # Robust normalization
            robust_normalized = df_robust[col]
            
            # Calculate "oscillation potential" - how often values cross zero
            basic_zero_crossings = np.sum(np.diff(np.sign(basic_normalized)) != 0)
            robust_zero_crossings = np.sum(np.diff(np.sign(robust_normalized)) != 0)
            
            # Calculate extreme value transitions
            basic_extreme_transitions = np.sum(
                (np.abs(basic_normalized[:-1]) > 2.0) & (np.abs(basic_normalized[1:]) > 2.0) &
                (np.sign(basic_normalized[:-1]) != np.sign(basic_normalized[1:]))
            )
            robust_extreme_transitions = np.sum(
                (np.abs(robust_normalized[:-1]) > 2.0) & (np.abs(robust_normalized[1:]) > 2.0) &
                (np.sign(robust_normalized[:-1]) != np.sign(robust_normalized[1:]))
            )
            
            print(f"   {col}:")
            print(f"     Basic: zero_crossings={basic_zero_crossings}, extreme_transitions={basic_extreme_transitions}")
            print(f"     Robust: zero_crossings={robust_zero_crossings}, extreme_transitions={robust_extreme_transitions}")
            
            improvement = (basic_extreme_transitions - robust_extreme_transitions) / max(basic_extreme_transitions, 1) * 100
            print(f"     Improvement: {improvement:.1f}% reduction in extreme transitions")
        
        print(f"\n✅ ROBUST NORMALIZATION TEST COMPLETED")
        print(f"   Expected benefits:")
        print(f"   - Reduced sensitivity to outliers")
        print(f"   - More stable statistics over time")
        print(f"   - Less extreme value oscillations")
        print(f"   - Better handling of heavy-tailed distributions")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing robust normalization: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_config():
    """Create a test configuration with robust normalization enabled."""
    
    try:
        with open('strategyConfig_scalp_1s.json', 'r') as f:
            config = json.load(f)
        
        # Enable robust normalization
        config['robustNormalization'] = {
            "enabled": True,
            "alpha": 0.05,  # More conservative for stability
            "description": "Robust orderbook volume normalization using median + MAD"
        }
        
        # Adjust thresholds for more stable trading
        config['tradeParams']['entryActionThreshold'] = 0.4
        config['tradeParams']['longEntryThreshold'] = 0.4
        config['tradeParams']['shortEntryThreshold'] = 0.4
        config['tradeParams']['exitActionThreshold'] = 0.3
        
        # Save test config
        output_file = 'strategyConfig_scalp_1s_robust.json'
        with open(output_file, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"✅ Created robust normalization config: {output_file}")
        return output_file
        
    except Exception as e:
        print(f"❌ Error creating test config: {e}")
        return None

def main():
    """Main test function."""
    
    print("🔬 ROBUST ORDERBOOK NORMALIZATION TEST")
    print("=" * 50)
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test normalization methods
    success = test_normalization_methods()
    
    if success:
        # Create test configuration
        test_config = create_test_config()
        
        if test_config:
            print(f"\n🚀 NEXT STEPS:")
            print(f"1. Test with robust normalization:")
            print(f"   python3 simulate_trading_new.py --config {test_config}")
            print(f"2. Compare threshold breach patterns")
            print(f"3. Look for reduced oscillation in new threshold logs")
            print(f"4. Monitor signal stability metrics")
    
    print(f"\n" + "="*50)
    print("Test completed!")

if __name__ == "__main__":
    main()
