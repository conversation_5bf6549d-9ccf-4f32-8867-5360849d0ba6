#!/usr/bin/env python3
"""
Quick Agent Health Check - Rapid diagnostic tool for agent stability issues.
Performs essential checks to identify common problems with the trading agent.
"""

import os
import sys
import json
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)

def check_configuration():
    """Quick configuration health check."""
    log.info("🔧 Configuration Health Check")
    
    config_file = "strategyConfig_scalp_1s_stable.json"
    if not os.path.exists(config_file):
        log.error(f"❌ Stable config not found: {config_file}")
        return False
    
    with open(config_file, 'r') as f:
        config = json.load(f)
    
    # Critical settings check
    trade_params = config.get('tradeParams', {})
    
    # Check threshold gap
    entry_thresh = trade_params.get('entryActionThreshold', 0)
    exit_thresh = trade_params.get('exitActionThreshold', 0)
    gap = entry_thresh - exit_thresh
    
    if gap >= 0.25:
        log.info(f"✅ Threshold gap OK: {gap:.2f}")
    else:
        log.error(f"❌ Threshold gap too small: {gap:.2f} (need >= 0.25)")
        return False
    
    # Check cooldown
    cooldown = trade_params.get('minTimeInTradeSeconds', 0)
    if cooldown >= 60:
        log.info(f"✅ Cooldown OK: {cooldown}s")
    else:
        log.warning(f"⚠️ Short cooldown: {cooldown}s (recommend >= 60s)")
    
    # Check VecNormalize bypass
    bypass = config.get('_vecnormalize_bypass_on_error', False)
    if bypass:
        log.info("✅ VecNormalize auto-bypass enabled")
    else:
        log.warning("⚠️ VecNormalize auto-bypass disabled")
    
    return True

def check_files():
    """Check essential files exist."""
    log.info("📁 File System Check")
    
    essential_files = [
        "simulate_trading_new.py",
        "test_stabilized_agent.py",
        "test_signal_variability_fix.py"
    ]
    
    missing = []
    for file in essential_files:
        if os.path.exists(file):
            log.info(f"✅ {file}")
        else:
            log.error(f"❌ {file}")
            missing.append(file)
    
    # Check model files
    model_paths = [
        "models/sac_2799104_steps",
        "models/vecnormalize_sac_2799104_steps.pkl"
    ]
    
    model_found = False
    for path in model_paths:
        if os.path.exists(path):
            log.info(f"✅ Model: {path}")
            model_found = True
            break
    
    if not model_found:
        log.warning("⚠️ No model files found - simulation will fail")
    
    return len(missing) == 0

def check_code_fixes():
    """Check if critical code fixes are present."""
    log.info("🔍 Code Fixes Check")
    
    sim_file = "simulate_trading_new.py"
    if not os.path.exists(sim_file):
        log.error(f"❌ {sim_file} not found")
        return False
    
    with open(sim_file, 'r') as f:
        content = f.read()
    
    fixes = [
        ("deterministic=True", "Deterministic prediction"),
        ("manual_normalization", "Manual normalization fallback"),
        ("AUTO-BYPASS", "VecNormalize auto-bypass"),
        ("torch.no_grad()", "Performance optimization"),
        ("entry_confirm", "Signal aggregation"),
        ("orderbook_normalization_enabled = True", "Orderbook normalization")
    ]
    
    all_good = True
    for fix_code, fix_name in fixes:
        if fix_code in content:
            log.info(f"✅ {fix_name}")
        else:
            log.error(f"❌ {fix_name} - missing: {fix_code}")
            all_good = False
    
    return all_good

def suggest_quick_fixes():
    """Suggest quick fixes for common issues."""
    log.info("💡 Quick Fix Suggestions")
    
    suggestions = [
        "1. 🔧 If getting 'KRITICKY NÍZKA VARIABILITA' errors:",
        "   - Set '_vecnormalize_disabled': true in config",
        "   - Run: python test_signal_variability_fix.py",
        "",
        "2. 🔧 If agent still oscillating:",
        "   - Increase entryActionThreshold to 0.7+",
        "   - Increase minTimeInTradeSeconds to 120+",
        "",
        "3. 🔧 If no trades happening:",
        "   - Lower entryActionThreshold to 0.5",
        "   - Check data quality and date range",
        "",
        "4. 🔧 If performance is slow:",
        "   - Verify torch.no_grad() is present",
        "   - Check orderbook normalization is enabled",
        "",
        "5. 🔧 For general debugging:",
        "   - Run: python diagnose_signal_variability.py",
        "   - Check logs for 'Manual diagnostika' messages"
    ]
    
    for suggestion in suggestions:
        log.info(suggestion)

def run_quick_test():
    """Suggest a quick test command."""
    log.info("🧪 Quick Test Command")
    log.info("Run this to test the stabilized agent:")
    log.info("python simulate_trading_new.py --cfg strategyConfig_scalp_1s_stable.json --start 2025-01-01 --end 2025-01-02")
    log.info("")
    log.info("Look for these success indicators:")
    log.info("- Signal std > 0.01 (not 0.0000)")
    log.info("- Threshold breaches > 0")
    log.info("- No 'KRITICKY NÍZKA VARIABILITA' errors")
    log.info("- 'Manual diagnostika' or 'VecNormalize diagnostika' in logs")

def main():
    """Run quick health check."""
    log.info("🚀 Quick Agent Health Check")
    log.info("=" * 50)
    
    checks_passed = 0
    total_checks = 3
    
    try:
        if check_configuration():
            checks_passed += 1
        log.info("")
        
        if check_files():
            checks_passed += 1
        log.info("")
        
        if check_code_fixes():
            checks_passed += 1
        log.info("")
        
        suggest_quick_fixes()
        log.info("")
        
        run_quick_test()
        log.info("")
        
    except Exception as e:
        log.error(f"❌ Health check failed: {e}")
    
    log.info("=" * 50)
    log.info(f"📊 Health Check Results: {checks_passed}/{total_checks} passed")
    
    if checks_passed == total_checks:
        log.info("✅ Agent appears healthy - ready for testing!")
    elif checks_passed >= 2:
        log.warning("⚠️ Agent mostly healthy - minor issues detected")
    else:
        log.error("❌ Agent has significant issues - review fixes needed")
    
    return 0 if checks_passed >= 2 else 1

if __name__ == "__main__":
    sys.exit(main())
