# Debug Signal Variability - Opravy implementované

## 🔍 **Identifikované problémy:**

### **1. Signal Aggregation príli<PERSON> agresívny**
```python
# PROBLÉM:
confidence_threshold = 0.65  # Pr<PERSON><PERSON><PERSON> vysoký!
# Agent generuje signály ~0.4-0.7
# Aggregation ich znižuje na ~0.3-0.5  
# Confidence 0.65 ich všetky odfiltruje → entry_sig = 0

# OPRAVA:
confidence_threshold = 0.3  # Znížené z 0.65
```

### **2. Robustná normalizácia môže byť príčina**
```python
# DOČASNE VYPNUTÉ:
orderbook_normalization_enabled = False  # Pre debugging
```

### **3. Thresholdy stále príliš vysoké**
```json
// ZNÍŽENÉ NA MINIMUM:
"entryActionThreshold": 0.1,  // z 0.4
"longEntryThreshold": 0.1,    // z 0.4  
"shortEntryThreshold": 0.1,   // z 0.4
"exitActionThreshold": 0.1    // z 0.3
```

## ✅ **Implementované opravy:**

### **1. Vypnutie signal aggregation**
```python
// DOČASNE VYPNUTÉ pre debugging:
entry_sig = action_ema  // Používa iba EMA, bez aggregation
```

### **2. Rozšírený debug logging**
```python
// Každých 500 krokov:
log.info("🔍 Signal processing debug:")
log.info(f"   Raw signal: {raw_entry_sig:.6f}")
log.info(f"   EMA signal: {action_ema:.6f}")  
log.info(f"   Final signal: {entry_sig:.6f}")
```

### **3. Vypnutie robustnej normalizácie**
```python
orderbook_normalization_enabled = False  // Dočasne
```

## 🧪 **Test plán:**

### **Krok 1: Test s minimálnymi thresholdmi**
```bash
python simulate_trading_new.py --cfg strategyConfig_scalp_1s.json --start 2025-07-01 --end 2025-07-02
```

### **Očakávané výsledky:**
- **Viac signálov**: >50 namiesto 3
- **Vyššia variabilita**: std > 0.1 namiesto 0.0000
- **Debug logy**: Ukážu skutočné hodnoty signálov

### **Krok 2: Postupné zapínanie funkcií**
1. **Ak funguje**: Postupne zvyšovať thresholdy
2. **Ak nefunguje**: Problém je hlbší (model/VecNormalize)

## 📊 **Diagnostické otázky:**

### **Ak sa zlepší variabilita:**
- **Signal aggregation** bol problém → Nastaviť confidence_threshold na 0.2-0.3
- **Robustná normalizácia** bola problém → Vypnúť alebo zmierniť
- **Thresholdy** boli problém → Používať 0.1-0.3

### **Ak sa nezlepší:**
- **Model problém** → Skúsiť iný checkpoint
- **VecNormalize problém** → Napriek tomu, že bol ukladaný počas tréningu
- **Feature problém** → Skontrolovať feature preparation

## 🎯 **Kľúčové metriky na sledovanie:**

### **V threshold logs:**
- **Počet signálov**: Cieľ >20/deň (namiesto 3)
- **Variabilita**: std > 0.1 (namiesto 0.0000)
- **Range**: Signály v rozsahu [-1, +1]

### **V debug logoch:**
- **Raw signals**: Rôznorodé hodnoty
- **EMA signals**: Vyhladzované ale variabilné  
- **Final signals**: Nie všetky nulové

## 💡 **Hypotézy o príčinách:**

### **Najpravdepodobnejšie:**
1. **Signal aggregation confidence_threshold=0.65** bol hlavný problém
2. **Robustná normalizácia** mohla "zabíjať" variabilitu orderbook features
3. **Kombinácia oboch** spôsobovala std=0.0000

### **Menej pravdepodobné:**
1. VecNormalize problém (ale bol ukladaný počas tréningu)
2. Model problém (ale fungoval predtým)
3. Feature preparation problém

## 🚀 **Akčný plán:**

### **Okamžite:**
1. Spustiť test s opravami
2. Skontrolovať debug logy
3. Porovnať threshold breach counts

### **Ak funguje:**
1. Postupne zapnúť signal aggregation s nižším confidence_threshold
2. Otestovať robustnú normalizáciu
3. Optimalizovať thresholdy

### **Ak nefunguje:**
1. Skúsiť iný model checkpoint
2. Skúsiť bez VecNormalize
3. Analyzovať feature preparation

**Opravy sú implementované a pripravené na testovanie!**
