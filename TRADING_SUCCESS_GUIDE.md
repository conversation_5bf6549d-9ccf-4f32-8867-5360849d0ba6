# Návod na riešenie oscilujúcich sign<PERSON>lov agenta

## 🔍 Anal<PERSON>za skutočného problému

Váš hlavný problém **NIE SÚ vysoké thresholdy**, ale **rýchlo oscilujúce extrémne signály**:

1. **R<PERSON><PERSON><PERSON> striedanie** sign<PERSON><PERSON>: +0.95 → -0.94 → +0.92 → -0.96
2. **Nejednoznačné smerovanie** - agent si "nevie vybrať"
3. **Nemožnosť obchodovania** - signály sa navzájom rušia
4. **Časové intervaly** - zmeny v sekund<PERSON>ch, nie minútach

## 🔬 Príčiny oscilujúcich signálov

### 1. **Nestabilné orderbook features**
- `ob_bid_vol_l1-5`, `ob_ask_vol_l5` sa menia každú sekundu
- Veľk<PERSON> skoky v objemo<PERSON> → extrémne zmeny v signáloch
- Agent reaguje na mikrozmeny → oscilovánie

### 2. **Chýbajúce signal smoothing**
- Live trading má EMA smoothing sign<PERSON>lov
- Simulácia používa surové signály
- Výsledok: nestabilné rozhodovanie

### 3. **VecNormalize problémy**
- Normalizácia môže spôsobiť drift
- Orderbook features majú rôzne distribúcie medzi dňami
- Extrémne hodnoty po normalizácii

## ⚡ Implementované riešenia

### 1. Signal Smoothing (PRIDANÉ)

```python
# Pridané do simulate_trading_new.py:
action_ema = 0.3 * raw_signal + 0.7 * previous_ema
entry_sig = action_ema  # Použiť smoothed signál
```

### 2. Analýza oscilácií

```bash
python analyze_signal_oscillation.py --threshold-file threshold_breaches_20250712_093253.txt
```

### 3. Test s novým smoothing

```bash
python simulate_trading_new.py --config strategyConfig_scalp_1s.json
```

## 🎯 Pokročilé riešenia

### A. Hybridný prístup - Agent + Technické indikátory

**Koncept**: Kombinovať agent signály s dodatočnými filtrami

```python
# Pseudokód pre hybridný systém
def hybrid_entry_decision(agent_signal, market_data):
    # 1. Agent musí dať signál
    if abs(agent_signal) < 0.4:
        return 0
    
    # 2. Technické potvrdenie
    rsi = market_data['RSI_14']
    ema_trend = market_data['EMA_9'] > market_data['EMA_21']
    volume_spike = market_data['volume'] > market_data['volume_ma_20'] * 1.5
    
    # 3. Kombinované rozhodnutie
    if agent_signal > 0.4:  # LONG signál
        if rsi < 70 and ema_trend and volume_spike:
            return 1
    elif agent_signal < -0.4:  # SHORT signál  
        if rsi > 30 and not ema_trend and volume_spike:
            return -1
    
    return 0
```

### B. Multi-timeframe prístup

**Koncept**: Agent na 1s + trend filter na 5m

```python
def multi_timeframe_decision(agent_1s, trend_5m):
    # Agent dáva entry/exit signály na 1s
    # Trend filter na 5m určuje smer
    
    if trend_5m == "BULLISH":
        return max(0, agent_1s)  # Iba LONG signály
    elif trend_5m == "BEARISH":  
        return min(0, agent_1s)  # Iba SHORT signály
    else:
        return agent_1s * 0.5  # Znížená agresivita v sideways
```

### C. Dynamické thresholdy podľa volatility

**Koncept**: Nižšie thresholdy pri vysokej volatilite

```python
def dynamic_threshold(base_threshold, atr, price):
    volatility_ratio = atr / price
    
    if volatility_ratio > 0.005:  # Vysoká volatilita
        return base_threshold * 0.7  # Nižší threshold
    elif volatility_ratio < 0.002:  # Nízka volatilita
        return base_threshold * 1.3  # Vyšší threshold
    else:
        return base_threshold
```

## 🚀 Stratégie pre maximalizáciu zisku

### 1. **Scalping Strategy** (Vaša aktuálna)
- **Timeframe**: 1s
- **Thresholdy**: 0.4-0.6
- **RR**: 1.5-2.0
- **Výhody**: Vysoká frekvencia, rýchle zisky
- **Nevýhody**: Vysoké fees, noise

### 2. **Trend Following Strategy**
- **Timeframe**: 1s entries, 5m trend
- **Thresholdy**: 0.3-0.5 (s trend filterom)
- **RR**: 2.0-3.0
- **Výhody**: Lepší win rate, menší noise
- **Nevýhody**: Menej obchodov

### 3. **Mean Reversion Strategy**
- **Timeframe**: 1s
- **Thresholdy**: 0.6-0.8 (kontra-trend)
- **RR**: 1.2-1.8
- **Výhody**: Vysoký win rate v sideways
- **Nevýhody**: Veľké straty v trendoch

### 4. **Breakout Strategy**
- **Timeframe**: 1s
- **Thresholdy**: 0.2-0.4 (s volume filterom)
- **RR**: 2.5-4.0
- **Výhody**: Veľké zisky pri breakoutoch
- **Nevýhody**: Veľa false breakoutov

## 📊 Odporúčané nastavenia pre rôzne ciele

### Pre maximálny počet obchodov:
```json
{
  "entryActionThreshold": 0.3,
  "longEntryThreshold": 0.3,
  "shortEntryThreshold": 0.3,
  "exitActionThreshold": 0.25,
  "rrTarget": 1.5
}
```

### Pre najlepší win rate:
```json
{
  "entryActionThreshold": 0.6,
  "longEntryThreshold": 0.6,
  "shortEntryThreshold": 0.6,
  "exitActionThreshold": 0.5,
  "rrTarget": 2.0
}
```

### Pre maximálny profit:
```json
{
  "entryActionThreshold": 0.4,
  "longEntryThreshold": 0.35,
  "shortEntryThreshold": 0.45,
  "exitActionThreshold": 0.3,
  "rrTarget": 2.5,
  "enableDynamicThresholds": true
}
```

## 🔧 Ďalšie optimalizácie

### 1. Position Sizing
- Zvýšiť size pri silných signáloch (>0.8)
- Znížiť size pri slabších signáloch (0.4-0.6)

### 2. Time Filters
- Obchodovať iba počas vysokej likvidity (8:00-22:00 UTC)
- Vyhýbať sa news eventom

### 3. Risk Management
- Max 3% risk per trade
- Max 10% daily drawdown
- Trailing stop loss pre veľké zisky

## 📈 Monitoring a ladenie

### Kľúčové metriky:
1. **Trade frequency**: 10-50 obchodov/deň
2. **Win rate**: 45-65%
3. **Profit factor**: >1.3
4. **Max drawdown**: <15%
5. **Sharpe ratio**: >1.0

### Varovania:
- ⚠️ Win rate <40% → Zvýšiť thresholdy
- ⚠️ <5 obchodov/deň → Znížiť thresholdy  
- ⚠️ Drawdown >20% → Znížiť position size
- ⚠️ Profit factor <1.1 → Prehodnotiť stratégiu

## 🎯 Akčný plán

1. **Okamžite**: Testovať s novými thresholdmi (0.6)
2. **Dnes**: Spustiť threshold optimization
3. **Tento týždeň**: Implementovať hybridný prístup
4. **Budúci týždeň**: Live testing s malými pozíciami

Úspech závisí od správneho vyváženia medzi frekvenciou obchodov a kvalitou signálov!
